# -*- coding: utf-8 -*-
import json

import yaml
import requests
from sqlalchemy import func

import pyrestful.rest
from Page.Pagenation import Pagenation
from api.log.log import CustomLogger
from db.model.alert_methods import AlertMethod
from db.model.hci.compute import Cluster
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
new_logger = CustomLogger()

class AlertMethodHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')

    def sync_alert_methods_to_hosts(self, session):
        """同步告警方式到所有主机的通用函数"""
        sync_errors = []

        try:
            # 获取所有告警方式用于同步到主机
            all_methods = session.query(AlertMethod).all()
            methods_data = [method.to_dict() for method in all_methods]

            # 获取所有集群并同步告警方式到主机
            clusters = session.query(Cluster).all()

            for cluster in clusters:
                for host in cluster.hosts:
                    if host.role != "manager":
                        continue
                    try:
                        url = f"http://{host.ip}:9178/alert/methods"
                        headers = {'Content-Type': 'application/json'}

                        # 发送JSON格式的数据
                        resp = requests.post(url, json=methods_data, headers=headers, timeout=5)

                        if resp.status_code == 200:
                            print(f"主机 {host.ip} 同步告警方式成功")
                        else:
                            error_msg = f"主机 {host.ip} 同步告警方式失败: HTTP {resp.status_code}"
                            print(error_msg)
                            sync_errors.append(error_msg)

                    except requests.exceptions.RequestException as e:
                        error_msg = f"主机 {host.ip} 同步告警方式失败: {str(e)}"
                        print(error_msg)
                        sync_errors.append(error_msg)
                    except Exception as e:
                        error_msg = f"主机 {host.ip} 同步告警方式失败: {str(e)}"
                        print(error_msg)
                        sync_errors.append(error_msg)

        except Exception as e:
            error_msg = f"同步告警方式到主机时发生错误: {str(e)}"
            print(error_msg)
            sync_errors.append(error_msg)

        return sync_errors

    @post(_path="/v1/alertsmethods/all", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_methods_query(self, form):
        try:
            # 解析请求中的分页和排序参数
            order_by = form.get("order_by", "")
            order_type = form.get("order_type", "desc")
            search_str = form.get("search_str", "")
            page = int(form.get("page", 1))
            pagecount = int(form.get("pagecount", 10))

            with self.session_scope() as session:
                # 查询所有方式
                all_methods = session.query(AlertMethod).all()

                # 如果有搜索字符串，则过滤结果
                if search_str:
                    all_methods = [method for method in all_methods if search_str.lower() in method.name.lower()]

                # 排序方式
                if order_type == "desc" and order_by:
                    all_methods = sorted(all_methods, key=lambda x: getattr(x, order_by), reverse=True)
                elif order_type == "asc" and order_by:
                    all_methods = sorted(all_methods, key=lambda x: getattr(x, order_by), reverse=False)

                # 分页
                pagenation = Pagenation(all_methods, page, pagecount)
                current_page_data = pagenation.show()
                total_pages = pagenation.total()

                # 准备返回的数据结构
                result = {
                    "total": len(all_methods),
                    "pages": total_pages,
                    "current_page": page,
                    "data": [method.to_dict() for method in current_page_data],
                }
                return result
        except Exception as e:
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

    @get(_path="/v1/alertsmethods/detail/{method_id}", _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_methods_detail(self, method_id):
        """获取单个告警方式详情"""
        try:
            method_id = int(method_id)
            with self.session_scope() as session:
                method = session.query(AlertMethod).filter(AlertMethod.id == method_id).first()
                if not method:
                    self.set_status(404)
                    return {"msg": "error", "detail": "告警方式不存在"}

                return {"msg": "ok", "data": method.to_dict()}

        except ValueError:
            self.set_status(400)
            return {"msg": "error", "detail": "无效的ID格式"}
        except Exception as e:
            self.set_status(502)
            return {"msg": "error", "detail": str(e)}

    @get(_path="/v1/alertsmethods/types", _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_methods_types(self):
        """获取支持的告警方式类型"""
        types = [
            {
                'type': 'email',
                'name': '邮件告警',
                'description': '通过SMTP发送邮件告警',
                'fields': [
                    {'field': 'smtp_from', 'name': 'SMTP发件人邮箱', 'type': 'email', 'required': True},
                    {'field': 'smtp_smarthost', 'name': 'SMTP服务器地址:端口', 'type': 'string', 'required': True},
                    {'field': 'smtp_auth_username', 'name': 'SMTP认证用户名', 'type': 'string', 'required': True},
                    {'field': 'smtp_auth_password', 'name': 'SMTP认证密码', 'type': 'password', 'required': True},
                    {'field': 'smtp_require_tls', 'name': '是否需要TLS', 'type': 'boolean', 'required': False},
                    {'field': 'email_to', 'name': '邮件接收人', 'type': 'string', 'required': True},
                    {'field': 'email_subject', 'name': '邮件主题模板', 'type': 'string', 'required': False},
                    {'field': 'email_body', 'name': '邮件内容模板', 'type': 'textarea', 'required': False}
                ]
            },
            {
                'type': 'dingtalk',
                'name': '钉钉告警',
                'description': '通过钉钉机器人发送告警',
                'fields': [
                    {'field': 'dingtalk_webhook_url', 'name': '钉钉Webhook地址', 'type': 'url', 'required': True},
                    {'field': 'dingtalk_secret', 'name': '钉钉加签密钥', 'type': 'string', 'required': False},
                    {'field': 'dingtalk_at_mobiles', 'name': '@手机号列表', 'type': 'string', 'required': False},
                    {'field': 'dingtalk_at_all', 'name': '是否@所有人', 'type': 'boolean', 'required': False},
                    {'field': 'dingtalk_title', 'name': '消息标题模板', 'type': 'string', 'required': False},
                    {'field': 'dingtalk_content', 'name': '消息内容模板', 'type': 'textarea', 'required': False}
                ]
            },
            {
                'type': 'webhook',
                'name': 'Webhook告警',
                'description': '通过HTTP请求发送告警',
                'fields': [
                    {'field': 'webhook_url', 'name': 'Webhook地址', 'type': 'url', 'required': True},
                    {'field': 'webhook_method', 'name': 'HTTP方法', 'type': 'select', 'options': ['POST', 'PUT'], 'required': False},
                    {'field': 'webhook_headers', 'name': 'HTTP请求头', 'type': 'textarea', 'required': False},
                    {'field': 'webhook_body', 'name': 'HTTP请求体模板', 'type': 'textarea', 'required': False},
                    {'field': 'webhook_timeout', 'name': '超时时间(秒)', 'type': 'number', 'required': False}
                ]
            },
            {
                'type': 'sms',
                'name': '短信告警',
                'description': '通过短信发送告警',
                'fields': [
                    {'field': 'sms_api_url', 'name': '短信API地址', 'type': 'url', 'required': True},
                    {'field': 'sms_api_key', 'name': 'API密钥', 'type': 'string', 'required': True},
                    {'field': 'sms_api_secret', 'name': 'API密钥', 'type': 'password', 'required': True},
                    {'field': 'sms_template_id', 'name': '短信模板ID', 'type': 'string', 'required': True},
                    {'field': 'sms_sign_name', 'name': '短信签名', 'type': 'string', 'required': False},
                    {'field': 'sms_phone_numbers', 'name': '接收号码列表', 'type': 'string', 'required': True},
                    {'field': 'sms_content', 'name': '短信内容模板', 'type': 'string', 'required': False}
                ]
            },
            {
                'type': 'wechat',
                'name': '企业微信告警',
                'description': '通过企业微信机器人发送告警',
                'fields': [
                    {'field': 'wechat_webhook_url', 'name': '企业微信Webhook地址', 'type': 'url', 'required': True},
                    {'field': 'wechat_mentioned_list', 'name': '@用户列表', 'type': 'string', 'required': False},
                    {'field': 'wechat_mentioned_mobile_list', 'name': '@手机号列表', 'type': 'string', 'required': False},
                    {'field': 'wechat_title', 'name': '消息标题模板', 'type': 'string', 'required': False},
                    {'field': 'wechat_content', 'name': '消息内容模板', 'type': 'textarea', 'required': False}
                ]
            }
        ]

        return {"msg": "ok", "data": types}

    @post(_path="/v1/alertsmethods/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_methods_add(self, data):
        role = self.get_cookie("role", "")
        if not data:
            return {"msg": "error", "detail": "请求数据为空"}

        # 验证必填字段
        required_fields = ['name', 'method_type']
        for field in required_fields:
            if field not in data or not data[field]:
                return {"msg": "error", "detail": f"缺少必填字段: {field}"}

        try:
            with self.session_scope() as session:
                # 如果设置为默认，先取消其他同类型的默认设置
                if data.get('is_default', 0) == 1:
                    session.query(AlertMethod).filter(
                        AlertMethod.method_type == data['method_type']
                    ).update({'is_default': 0})

                # 创建新的告警方式
                new_method = AlertMethod()

                # 设置基本字段
                new_method.name = data['name']
                new_method.method_type = data['method_type']
                new_method.description = data.get('description', '')
                new_method.is_enabled = data.get('is_enabled', 1)
                new_method.is_default = data.get('is_default', 0)
                new_method.order_id = data.get('order_id', 0)
                new_method.status = data.get('status', 'active')

                # 设置邮件字段
                if data['method_type'] == 'email':
                    new_method.smtp_from = data.get('smtp_from')
                    new_method.smtp_smarthost = data.get('smtp_smarthost')
                    new_method.smtp_auth_username = data.get('smtp_auth_username')
                    new_method.smtp_auth_password = data.get('smtp_auth_password')
                    new_method.smtp_require_tls = data.get('smtp_require_tls', 1)
                    new_method.email_to = data.get('email_to')
                    new_method.email_subject = data.get('email_subject')
                    new_method.email_body = data.get('email_body')

                # 设置钉钉字段
                elif data['method_type'] == 'dingtalk':
                    new_method.dingtalk_webhook_url = data.get('dingtalk_webhook_url')
                    new_method.dingtalk_secret = data.get('dingtalk_secret')
                    new_method.dingtalk_at_mobiles = data.get('dingtalk_at_mobiles')
                    new_method.dingtalk_at_all = data.get('dingtalk_at_all', 0)
                    new_method.dingtalk_title = data.get('dingtalk_title')
                    new_method.dingtalk_content = data.get('dingtalk_content')

                # 设置Webhook字段
                elif data['method_type'] == 'webhook':
                    new_method.webhook_url = data.get('webhook_url')
                    new_method.webhook_method = data.get('webhook_method', 'POST')
                    new_method.webhook_headers = data.get('webhook_headers')
                    new_method.webhook_body = data.get('webhook_body')
                    new_method.webhook_timeout = data.get('webhook_timeout', 10)

                # 设置短信字段
                elif data['method_type'] == 'sms':
                    new_method.sms_api_url = data.get('sms_api_url')
                    new_method.sms_api_key = data.get('sms_api_key')
                    new_method.sms_api_secret = data.get('sms_api_secret')
                    new_method.sms_template_id = data.get('sms_template_id')
                    new_method.sms_sign_name = data.get('sms_sign_name')
                    new_method.sms_phone_numbers = data.get('sms_phone_numbers')
                    new_method.sms_content = data.get('sms_content')

                # 设置企业微信字段
                elif data['method_type'] == 'wechat':
                    new_method.wechat_webhook_url = data.get('wechat_webhook_url')
                    new_method.wechat_mentioned_list = data.get('wechat_mentioned_list')
                    new_method.wechat_mentioned_mobile_list = data.get('wechat_mentioned_mobile_list')
                    new_method.wechat_title = data.get('wechat_title')
                    new_method.wechat_content = data.get('wechat_content')

                session.add(new_method)
                session.commit()

                # 同步告警方式到所有主机
                sync_errors = self.sync_alert_methods_to_hosts(session)

            new_logger.log(
                self.username, "告警方式管理", "创建告警方式", "成功", role, "{}:{},成功".format("创建告警方式", data.get("name", ""))
            )

            # 返回结果，包含同步错误信息（如果有的话）
            result = {"msg": "ok", "data": new_method.to_dict()}
            if sync_errors:
                result["msg"] = sync_errors
            return result

        except Exception as e:
            new_logger.log(
                self.username, "告警方式管理", "创建告警方式", "失败", role, "{}:{},失败".format("创建告警方式", data.get("name", ""))
            )
            self.set_status(502)
            return {"msg": "error", "detail": str(e)}

    @put(_path="/v1/alertsmethods/edit", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_methods_edit(self, data):
        role = self.get_cookie("role", "")

        # 输入验证
        if not data:
            return {"msg": "error", "detail": "请求数据为空"}

        method_id = data.get("id")
        if not method_id:
            return {"msg": "error", "detail": "请提供要修改的方式ID"}

        try:
            with self.session_scope() as session:
                # 查找要更新的方式
                old_method = session.query(AlertMethod).filter(AlertMethod.id == method_id).first()
                if not old_method:
                    return {"msg": "error", "detail": "找不到指定ID的方式"}

                # 如果设置为默认，先取消其他同类型的默认设置
                if data.get('is_default', 0) == 1 and old_method.is_default != 1:
                    session.query(AlertMethod).filter(
                        AlertMethod.method_type == old_method.method_type,
                        AlertMethod.id != method_id
                    ).update({'is_default': 0})

                # 更新基本字段
                basic_fields = ['name', 'method_type', 'description', 'is_enabled', 'is_default', 'order_id', 'status']
                for field in basic_fields:
                    if field in data:
                        setattr(old_method, field, data[field])

                # 更新邮件字段
                email_fields = ['smtp_from', 'smtp_smarthost', 'smtp_auth_username', 'smtp_auth_password',
                               'smtp_require_tls', 'email_to', 'email_subject', 'email_body']
                for field in email_fields:
                    if field in data:
                        setattr(old_method, field, data[field])

                # 更新钉钉字段
                dingtalk_fields = ['dingtalk_webhook_url', 'dingtalk_secret', 'dingtalk_at_mobiles',
                                  'dingtalk_at_all', 'dingtalk_title', 'dingtalk_content']
                for field in dingtalk_fields:
                    if field in data:
                        setattr(old_method, field, data[field])

                # 更新Webhook字段
                webhook_fields = ['webhook_url', 'webhook_method', 'webhook_headers', 'webhook_body', 'webhook_timeout']
                for field in webhook_fields:
                    if field in data:
                        setattr(old_method, field, data[field])

                # 更新短信字段
                sms_fields = ['sms_api_url', 'sms_api_key', 'sms_api_secret', 'sms_template_id',
                             'sms_sign_name', 'sms_phone_numbers', 'sms_content']
                for field in sms_fields:
                    if field in data:
                        setattr(old_method, field, data[field])

                # 更新企业微信字段
                wechat_fields = ['wechat_webhook_url', 'wechat_mentioned_list', 'wechat_mentioned_mobile_list',
                                'wechat_title', 'wechat_content']
                for field in wechat_fields:
                    if field in data:
                        setattr(old_method, field, data[field])

                # 提交更新
                session.commit()

                # 获取更新后的方式数据用于同步到主机
                updated_method = old_method

                # 同步告警方式到所有主机
                sync_errors = self.sync_alert_methods_to_hosts(session)

            # 记录成功日志
            method_name = data.get('name', updated_method.name if updated_method else 'Unknown')
            new_logger.log(
                self.username, "告警方式管理", "修改告警方式", "成功", role,
                f"修改告警方式:{method_id},成功: {method_name}"
            )

            # 返回结果，包含同步错误信息（如果有的话）
            result = {"msg": "ok", "data": updated_method.to_dict()}
            if sync_errors:
                result["msg"] = sync_errors

            return result

        except Exception as e:
            # 记录详细的错误信息
            error_detail = str(e)
            method_name = data.get('name', 'Unknown')

            new_logger.log(
                self.username, "告警方式管理", "修改告警方式", "失败", role,
                f"修改告警方式:{method_id},失败: {method_name}, 错误: {error_detail}"
            )

            print(f"修改告警方式失败: {error_detail}")
            self.set_status(500)  # Internal Server Error
            return {"msg": "error", "detail": f"修改告警方式失败: {error_detail}"}

    @delete(_path="/v1/alertsmethods/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_methods_delete_batch(self, form):
        role = self.get_cookie("role", "")
        try:
            ids_to_delete = form["ids"]

            with self.session_scope() as session:
                # 查询所有要删除的方式
                methods_to_delete = session.query(AlertMethod).filter(AlertMethod.id.in_(ids_to_delete)).all()

                # 过滤出非默认方式及其名称
                non_default_ids = []
                non_default_names = []

                for method in methods_to_delete:
                    non_default_ids.append(method.id)
                    non_default_names.append(method.name)

                session.query(AlertMethod).filter(AlertMethod.id.in_(non_default_ids)).delete(synchronize_session='fetch')
                session.commit()

                # 同步告警方式到所有主机
                sync_errors = self.sync_alert_methods_to_hosts(session)

                # 日志记录
                deleted_names = ", ".join(non_default_names)
                print("告警方式管理", "批量删除告警方式", "成功", role, f"批量删除告警方式:{deleted_names},成功")
                new_logger.log(
                    self.username, "告警方式管理", "批量删除告警方式", "成功", role, f"批量删除告警方式:{deleted_names},成功"
                )

                # 返回结果，包含同步错误信息（如果有的话）
                result = {"msg": "ok"}
                if sync_errors:
                    result["msg"] = sync_errors
                return result
        except Exception as e:
            new_logger.log(
                self.username, "告警方式管理", "批量删除告警方式", "失败", role, f"批量删除告警方式,失败"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

