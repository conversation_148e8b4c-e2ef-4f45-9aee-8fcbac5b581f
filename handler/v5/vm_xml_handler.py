# -*- coding: utf-8 -*-

import traceback
import pyrestful.rest
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from sqlalchemy import desc, asc
from sqlalchemy.orm import sessionmaker
from db.model.hci.compute import Domain, DomainXml
from db.model.user import User
from api.log.log import CustomLogger
import xml.etree.ElementTree as ET
from xml.dom import minidom
import datetime

from util.decorators import role_required

new_logger = CustomLogger()


class VmXmlHandler(pyrestful.rest.RestHandler):
    """虚拟机XML配置管理接口"""

    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')

    @property
    def session_scope(self):
        return self.application.session_scope

    @property
    def username(self):
        return self.get_cookie('username', "")

    def validate_xml(self, xml_content):
        """验证XML格式是否正确"""
        try:
            ET.fromstring(xml_content)
            return True, ""
        except ET.ParseError as e:
            return False, str(e)

    def format_xml(self, xml_content):
        """格式化XML内容"""
        try:
            dom = minidom.parseString(xml_content)
            return dom.toprettyxml(indent="  ")
        except Exception as e:
            return xml_content

    @role_required()
    @post(_path="/v5/vm/{domain_id}/xml/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def create_vm_xml(self, domain_id, form):
        """
        创建虚拟机XML配置
        """
        username = self.username
        xml_content = form.get("xml_content", "")
        xml_type = form.get("xml_type", "domain")
        description = form.get("description", "")

        if not xml_content:
            return {"msg": "XML内容不能为空", "code": 400}

        # 验证XML格式
        is_valid, error_msg = self.validate_xml(xml_content)
        if not is_valid:
            return {"msg": f"XML格式错误: {error_msg}", "code": 400}

        try:
            with self.session_scope() as session:
                # 检查虚拟机是否存在
                domain = session.query(Domain).filter(Domain.id == domain_id).first()
                if not domain:
                    return {"msg": "虚拟机不存在", "code": 404}

                # 如果设置为活跃版本，先将其他版本设为非活跃
                if form.get("is_active", True):
                    session.query(DomainXml).filter(
                        DomainXml.domain_id == domain_id,
                        DomainXml.xml_type == xml_type
                    ).update({"is_active": False})

                # 获取下一个版本号
                max_version = session.query(DomainXml).filter(
                    DomainXml.domain_id == domain_id,
                    DomainXml.xml_type == xml_type
                ).order_by(desc(DomainXml.version)).first()

                next_version = (max_version.version + 1) if max_version else 1

                # 创建新的XML配置
                domain_xml = DomainXml(
                    domain_id=domain_id,
                    xml_content=xml_content,
                    xml_type=xml_type,
                    version=next_version,
                    is_active=form.get("is_active", True),
                    description=description,
                    created_by=username
                )

                session.add(domain_xml)
                session.commit()

                new_logger.log(
                    username, "虚拟机XML", "创建", "成功", username,
                    f"为虚拟机 {domain.name} 创建XML配置版本 {next_version}"
                )

                return {
                    "msg": "XML配置创建成功",
                    "code": 200,
                    "data": {
                        "id": str(domain_xml.id),
                        "version": next_version
                    }
                }

        except Exception as e:
            traceback.print_exc()
            new_logger.log(
                username, "虚拟机XML", "创建", "失败", username,
                f"为虚拟机创建XML配置失败: {str(e)}"
            )
            return {"msg": f"创建失败: {str(e)}", "code": 500}

    @get(_path="/v5/vm/{domain_id}/xml/list", _produces=mediatypes.APPLICATION_JSON)
    def get_vm_xml_list(self, domain_id):
        """
        获取虚拟机XML配置列表
        """
        xml_type = self.get_argument("xml_type", "domain")
        page = int(self.get_argument("page", 1))
        page_size = int(self.get_argument("page_size", 10))

        try:
            with self.session_scope() as session:
                # 检查虚拟机是否存在
                domain = session.query(Domain).filter(Domain.id == domain_id).first()
                if not domain:
                    return {"msg": "虚拟机不存在", "code": 404}

                # 查询XML配置列表
                query = session.query(DomainXml).filter(
                    DomainXml.domain_id == domain_id,
                    DomainXml.xml_type == xml_type
                ).order_by(desc(DomainXml.version))

                total = query.count()
                xml_configs = query.offset((page - 1) * page_size).limit(page_size).all()

                data = []
                for xml_config in xml_configs:
                    data.append({
                        "id": str(xml_config.id),
                        "version": xml_config.version,
                        "xml_type": xml_config.xml_type,
                        "is_active": xml_config.is_active,
                        "description": xml_config.description,
                        "created_at": xml_config.created_at.isoformat() if xml_config.created_at else None,
                        "updated_at": xml_config.updated_at.isoformat() if xml_config.updated_at else None,
                        "created_by": xml_config.created_by
                    })

                return {
                    "msg": "查询成功",
                    "code": 200,
                    "data": {
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "items": data
                    }
                }

        except Exception as e:
            traceback.print_exc()
            return {"msg": f"查询失败: {str(e)}", "code": 500}

    @get(_path="/v5/vm/xml/{xml_id}/detail", _produces=mediatypes.APPLICATION_JSON)
    def get_vm_xml_detail(self, xml_id):
        """
        获取XML配置详情
        """
        try:
            with self.session_scope() as session:
                xml_config = session.query(DomainXml).filter(DomainXml.id == xml_id).first()
                if not xml_config:
                    return {"msg": "XML配置不存在", "code": 404}

                # 获取关联的虚拟机信息
                domain = session.query(Domain).filter(Domain.id == xml_config.domain_id).first()

                data = {
                    "id": str(xml_config.id),
                    "domain_id": str(xml_config.domain_id),
                    "domain_name": domain.name if domain else "",
                    "xml_content": xml_config.xml_content,
                    "xml_type": xml_config.xml_type,
                    "version": xml_config.version,
                    "is_active": xml_config.is_active,
                    "description": xml_config.description,
                    "created_at": xml_config.created_at.isoformat() if xml_config.created_at else None,
                    "updated_at": xml_config.updated_at.isoformat() if xml_config.updated_at else None,
                    "created_by": xml_config.created_by
                }

                return {
                    "msg": "查询成功",
                    "code": 200,
                    "data": data
                }

        except Exception as e:
            traceback.print_exc()
            return {"msg": f"查询失败: {str(e)}", "code": 500}

    @role_required()
    @put(_path="/v5/vm/xml/{xml_id}/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def update_vm_xml(self, xml_id, form):
        """
        更新XML配置
        """
        username = self.username
        xml_content = form.get("xml_content")
        description = form.get("description")
        is_active = form.get("is_active")

        try:
            with self.session_scope() as session:
                xml_config = session.query(DomainXml).filter(DomainXml.id == xml_id).first()
                if not xml_config:
                    return {"msg": "XML配置不存在", "code": 404}

                # 如果更新XML内容，验证格式
                if xml_content is not None:
                    is_valid, error_msg = self.validate_xml(xml_content)
                    if not is_valid:
                        return {"msg": f"XML格式错误: {error_msg}", "code": 400}
                    xml_config.xml_content = xml_content

                # 如果设置为活跃版本，先将其他版本设为非活跃
                if is_active is True:
                    session.query(DomainXml).filter(
                        DomainXml.domain_id == xml_config.domain_id,
                        DomainXml.xml_type == xml_config.xml_type,
                        DomainXml.id != xml_id
                    ).update({"is_active": False})
                    xml_config.is_active = True
                elif is_active is False:
                    xml_config.is_active = False

                if description is not None:
                    xml_config.description = description

                xml_config.updated_at = datetime.datetime.now()
                session.commit()

                # 获取关联的虚拟机信息用于日志
                domain = session.query(Domain).filter(Domain.id == xml_config.domain_id).first()
                domain_name = domain.name if domain else "未知"

                new_logger.log(
                    username, "虚拟机XML", "更新", "成功", username,
                    f"更新虚拟机 {domain_name} 的XML配置版本 {xml_config.version}"
                )

                return {"msg": "XML配置更新成功", "code": 200}

        except Exception as e:
            traceback.print_exc()
            new_logger.log(
                username, "虚拟机XML", "更新", "失败", username,
                f"更新XML配置失败: {str(e)}"
            )
            return {"msg": f"更新失败: {str(e)}", "code": 500}

    @role_required()
    @delete(_path="/v5/vm/xml/{xml_id}/delete", _produces=mediatypes.APPLICATION_JSON)
    def delete_vm_xml(self, xml_id):
        """
        删除XML配置
        """
        username = self.username

        try:
            with self.session_scope() as session:
                xml_config = session.query(DomainXml).filter(DomainXml.id == xml_id).first()
                if not xml_config:
                    return {"msg": "XML配置不存在", "code": 404}

                # 检查是否为活跃版本
                if xml_config.is_active:
                    return {"msg": "不能删除活跃版本的XML配置", "code": 400}

                # 获取关联的虚拟机信息用于日志
                domain = session.query(Domain).filter(Domain.id == xml_config.domain_id).first()
                domain_name = domain.name if domain else "未知"
                version = xml_config.version

                session.delete(xml_config)
                session.commit()

                new_logger.log(
                    username, "虚拟机XML", "删除", "成功", username,
                    f"删除虚拟机 {domain_name} 的XML配置版本 {version}"
                )

                return {"msg": "XML配置删除成功", "code": 200}

        except Exception as e:
            traceback.print_exc()
            new_logger.log(
                username, "虚拟机XML", "删除", "失败", username,
                f"删除XML配置失败: {str(e)}"
            )
            return {"msg": f"删除失败: {str(e)}", "code": 500}

    @role_required()
    @post(_path="/v5/vm/xml/{xml_id}/activate", _produces=mediatypes.APPLICATION_JSON)
    def activate_vm_xml(self, xml_id):
        """
        激活指定版本的XML配置
        """
        username = self.username

        try:
            with self.session_scope() as session:
                xml_config = session.query(DomainXml).filter(DomainXml.id == xml_id).first()
                if not xml_config:
                    return {"msg": "XML配置不存在", "code": 404}

                # 将同类型的其他版本设为非活跃
                session.query(DomainXml).filter(
                    DomainXml.domain_id == xml_config.domain_id,
                    DomainXml.xml_type == xml_config.xml_type,
                    DomainXml.id != xml_id
                ).update({"is_active": False})

                # 激活当前版本
                xml_config.is_active = True
                xml_config.updated_at = datetime.datetime.now()
                session.commit()

                # 获取关联的虚拟机信息用于日志
                domain = session.query(Domain).filter(Domain.id == xml_config.domain_id).first()
                domain_name = domain.name if domain else "未知"

                new_logger.log(
                    username, "虚拟机XML", "激活", "成功", username,
                    f"激活虚拟机 {domain_name} 的XML配置版本 {xml_config.version}"
                )

                return {"msg": "XML配置激活成功", "code": 200}

        except Exception as e:
            traceback.print_exc()
            new_logger.log(
                username, "虚拟机XML", "激活", "失败", username,
                f"激活XML配置失败: {str(e)}"
            )
            return {"msg": f"激活失败: {str(e)}", "code": 500}

    @get(_path="/v5/vm/{domain_id}/xml/active", _produces=mediatypes.APPLICATION_JSON)
    def get_active_vm_xml(self, domain_id):
        """
        获取虚拟机当前活跃的XML配置
        """
        xml_type = self.get_argument("xml_type", "domain")

        try:
            with self.session_scope() as session:
                # 检查虚拟机是否存在
                domain = session.query(Domain).filter(Domain.id == domain_id).first()
                if not domain:
                    return {"msg": "虚拟机不存在", "code": 404}

                # 查询活跃的XML配置
                xml_config = session.query(DomainXml).filter(
                    DomainXml.domain_id == domain_id,
                    DomainXml.xml_type == xml_type,
                    DomainXml.is_active == True
                ).first()

                if not xml_config:
                    return {"msg": "未找到活跃的XML配置", "code": 404}

                data = {
                    "id": str(xml_config.id),
                    "domain_id": str(xml_config.domain_id),
                    "domain_name": domain.name,
                    "xml_content": xml_config.xml_content,
                    "xml_type": xml_config.xml_type,
                    "version": xml_config.version,
                    "is_active": xml_config.is_active,
                    "description": xml_config.description,
                    "created_at": xml_config.created_at.isoformat() if xml_config.created_at else None,
                    "updated_at": xml_config.updated_at.isoformat() if xml_config.updated_at else None,
                    "created_by": xml_config.created_by
                }

                return {
                    "msg": "查询成功",
                    "code": 200,
                    "data": data
                }

        except Exception as e:
            traceback.print_exc()
            return {"msg": f"查询失败: {str(e)}", "code": 500}

    @post(_path="/v5/vm/xml/validate", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def validate_vm_xml(self, form):
        """
        验证XML格式
        """
        xml_content = form.get("xml_content", "")

        if not xml_content:
            return {"msg": "XML内容不能为空", "code": 400}

        is_valid, error_msg = self.validate_xml(xml_content)

        if is_valid:
            formatted_xml = self.format_xml(xml_content)
            return {
                "msg": "XML格式正确",
                "code": 200,
                "data": {
                    "is_valid": True,
                    "formatted_xml": formatted_xml
                }
            }
        else:
            return {
                "msg": "XML格式错误",
                "code": 400,
                "data": {
                    "is_valid": False,
                    "error": error_msg
                }
            }
