from sqlalchemy import *
from migrate import *

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('password_updated_record', meta, autoload=True)
    
    # 修改列类型为String(36)
    users.c.user_id.alter(type=String(64))

def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('password_updated_record', meta, autoload=True)
    
    # 修改回Integer类型
    users.c.user_id.alter(type=Integer)