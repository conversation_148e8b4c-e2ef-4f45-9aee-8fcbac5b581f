from sqlalchemy import *
from migrate import *

meta = MetaData()

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 1. 修改 ceph_cluster 表，添加缺失字段
    ceph_cluster = Table('ceph_cluster', meta, autoload=True)
    
    # 添加集群唯一标识符
    if 'cluster_fsid' not in ceph_cluster.c:
        cluster_fsid_col = Column('cluster_fsid', String(100), nullable=False, comment='集群唯一标识符')
        cluster_fsid_col.create(ceph_cluster)
    
    # 添加管理员用户名
    if 'admin_username' not in ceph_cluster.c:
        admin_username_col = Column('admin_username', String(100), nullable=False, default='admin', comment='管理员用户名')
        admin_username_col.create(ceph_cluster)
    
    # 添加管理员密钥
    if 'admin_keyring' not in ceph_cluster.c:
        admin_keyring_col = Column('admin_keyring', Text, nullable=False, comment='管理员密钥')
        admin_keyring_col.create(ceph_cluster)
    
    # 添加MDS节点数量
    if 'mds_count' not in ceph_cluster.c:
        mds_count_col = Column('mds_count', Integer, nullable=True, default=0, comment='MDS节点数量')
        mds_count_col.create(ceph_cluster)
    
    # 添加容量信息
    if 'total_capacity' not in ceph_cluster.c:
        total_capacity_col = Column('total_capacity', BigInteger, nullable=True, comment='集群总容量(字节)')
        total_capacity_col.create(ceph_cluster)
    
    if 'used_capacity' not in ceph_cluster.c:
        used_capacity_col = Column('used_capacity', BigInteger, nullable=True, comment='已使用容量(字节)')
        used_capacity_col.create(ceph_cluster)
    
    if 'available_capacity' not in ceph_cluster.c:
        available_capacity_col = Column('available_capacity', BigInteger, nullable=True, comment='可用容量(字节)')
        available_capacity_col.create(ceph_cluster)
    
    # 添加最后同步时间
    if 'last_sync_time' not in ceph_cluster.c:
        last_sync_time_col = Column('last_sync_time', DateTime(timezone=True), nullable=True, comment='最后同步时间')
        last_sync_time_col.create(ceph_cluster)
    
    # 2. 修改 ceph_pool 表，添加缺失字段
    ceph_pool = Table('ceph_pool', meta, autoload=True)
    
    # 修改外键字段名（如果需要）
    if 'ceph_id' in ceph_pool.c and 'ceph_cluster_id' not in ceph_pool.c:
        # 重命名字段
        ceph_pool.c.ceph_id.alter(name='ceph_cluster_id')
    
    # 添加Ceph池名称
    if 'ceph_pool_name' not in ceph_pool.c:
        ceph_pool_name_col = Column('ceph_pool_name', String(100), nullable=False, comment='Ceph中的池名称')
        ceph_pool_name_col.create(ceph_pool)
    
    # 添加Ceph池ID
    if 'ceph_pool_id' not in ceph_pool.c:
        ceph_pool_id_col = Column('ceph_pool_id', Integer, nullable=False, comment='Ceph中的池ID')
        ceph_pool_id_col.create(ceph_pool)
    
    # 添加池用户名
    if 'pool_username' not in ceph_pool.c:
        pool_username_col = Column('pool_username', String(100), nullable=False, comment='访问此池的用户名')
        pool_username_col.create(ceph_pool)
    
    # 添加池密钥
    if 'pool_keyring' not in ceph_pool.c:
        pool_keyring_col = Column('pool_keyring', Text, nullable=False, comment='访问此池的密钥')
        pool_keyring_col.create(ceph_pool)
    
    # 添加最小副本数
    if 'min_size' not in ceph_pool.c:
        min_size_col = Column('min_size', Integer, default=2, nullable=True, comment='最小副本数')
        min_size_col.create(ceph_pool)
    
    # 添加PGP数量
    if 'pgp_count' not in ceph_pool.c:
        pgp_count_col = Column('pgp_count', Integer, nullable=True, comment='PGP数量')
        pgp_count_col.create(ceph_pool)
    
    # 添加CRUSH规则名称
    if 'crush_rule_name' not in ceph_pool.c:
        crush_rule_name_col = Column('crush_rule_name', String(100), nullable=True, comment='CRUSH规则名称')
        crush_rule_name_col.create(ceph_pool)
    
    # 添加配额设置
    if 'quota_max_objects' not in ceph_pool.c:
        quota_max_objects_col = Column('quota_max_objects', BigInteger, nullable=True, comment='最大对象数量配额')
        quota_max_objects_col.create(ceph_pool)
    
    if 'quota_max_bytes' not in ceph_pool.c:
        quota_max_bytes_col = Column('quota_max_bytes', BigInteger, nullable=True, comment='最大字节数配额')
        quota_max_bytes_col.create(ceph_pool)
    
    # 添加统计信息
    if 'objects_count' not in ceph_pool.c:
        objects_count_col = Column('objects_count', BigInteger, nullable=True, default=0, comment='对象数量')
        objects_count_col.create(ceph_pool)
    
    if 'stored_bytes' not in ceph_pool.c:
        stored_bytes_col = Column('stored_bytes', BigInteger, nullable=True, default=0, comment='存储字节数')
        stored_bytes_col.create(ceph_pool)
    
    # 3. 修改 ceph_osd 表，添加缺失字段
    ceph_osd = Table('ceph_osd', meta, autoload=True)
    
    # 修改外键字段名（如果需要）
    if 'ceph_id' in ceph_osd.c and 'ceph_cluster_id' not in ceph_osd.c:
        # 重命名字段
        ceph_osd.c.ceph_id.alter(name='ceph_cluster_id')
    
    # 添加容量信息
    if 'total_capacity' not in ceph_osd.c:
        total_capacity_col = Column('total_capacity', BigInteger, nullable=True, comment='OSD总容量')
        total_capacity_col.create(ceph_osd)
    
    if 'used_capacity' not in ceph_osd.c:
        used_capacity_col = Column('used_capacity', BigInteger, nullable=True, comment='已使用容量')
        used_capacity_col.create(ceph_osd)
    
    if 'available_capacity' not in ceph_osd.c:
        available_capacity_col = Column('available_capacity', BigInteger, nullable=True, comment='可用容量')
        available_capacity_col.create(ceph_osd)


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 1. 回滚 ceph_cluster 表的修改
    ceph_cluster = Table('ceph_cluster', meta, autoload=True)
    
    # 删除新增的字段
    fields_to_remove_cluster = [
        'cluster_fsid', 'admin_username', 'admin_keyring', 'mds_count',
        'total_capacity', 'used_capacity', 'available_capacity', 'last_sync_time'
    ]
    
    for field in fields_to_remove_cluster:
        if field in ceph_cluster.c:
            ceph_cluster.c[field].drop()
    
    # 2. 回滚 ceph_pool 表的修改
    ceph_pool = Table('ceph_pool', meta, autoload=True)
    
    # 删除新增的字段
    fields_to_remove_pool = [
        'ceph_pool_name', 'ceph_pool_id', 'pool_username', 'pool_keyring',
        'min_size', 'pgp_count', 'crush_rule_name', 'quota_max_objects',
        'quota_max_bytes', 'objects_count', 'stored_bytes'
    ]
    
    for field in fields_to_remove_pool:
        if field in ceph_pool.c:
            ceph_pool.c[field].drop()
    
    # 恢复字段名（如果之前重命名了）
    if 'ceph_cluster_id' in ceph_pool.c:
        ceph_pool.c.ceph_cluster_id.alter(name='ceph_id')
    
    # 3. 回滚 ceph_osd 表的修改
    ceph_osd = Table('ceph_osd', meta, autoload=True)
    
    # 删除新增的字段
    fields_to_remove_osd = ['total_capacity', 'used_capacity', 'available_capacity']
    
    for field in fields_to_remove_osd:
        if field in ceph_osd.c:
            ceph_osd.c[field].drop()
    
    # 恢复字段名（如果之前重命名了）
    if 'ceph_cluster_id' in ceph_osd.c:
        ceph_osd.c.ceph_cluster_id.alter(name='ceph_id')