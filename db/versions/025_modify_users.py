from sqlalchemy import *
from migrate import *

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('users', meta, autoload=True)

    # 添加 ukey_status 字段，字符串类型，可为空
    ukey_status = Column('ukey_status', String(255), nullable=True)
    ukey_status.create(users)

    # 添加 ukey_write 字段，字符串类型，可为空
    ukey_write = Column('ukey_write', String(255), nullable=True)
    ukey_write.create(users)


def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('users', meta, autoload=True)
    users.c.ukey_status.drop()
    users.c.ukey_write.drop()