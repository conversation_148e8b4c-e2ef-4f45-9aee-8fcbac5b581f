import datetime
from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, Text, Boolean

meta = MetaData()

domain_xml = Table(
    'domain_xml', meta,
    Column('id', String(36), primary_key=True, nullable=False, comment='主键'),
    Column('domain_id', String(36), nullable=False, comment='(外键)虚拟机ID'),
    <PERSON>umn('xml_content', Text, nullable=False, comment='虚拟机XML配置内容'),
    <PERSON>umn('xml_type', String(50), nullable=False, default='domain', comment='XML类型：domain, storage等'),
    Column('version', Integer, nullable=False, default=1, comment='XML版本号'),
    Column('is_active', Boolean, nullable=False, default=True, comment='是否为当前活跃版本'),
    <PERSON>umn('description', String(512), nullable=True, comment='XML配置描述'),
    Column('created_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='创建时间'),
    <PERSON>umn('updated_at', DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='更新时间'),
    Column('created_by', String(100), nullable=True, comment='创建者'),
    ForeignKeyConstraint(['domain_id'], ['domain.id'], ondelete='CASCADE'),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    domain_xml.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    domain_xml.drop()
