from sqlalchemy import *
from migrate import *
import datetime

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, Boolean

meta = MetaData()

login_failure_records = Table(
    'login_failure_records', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    <PERSON><PERSON><PERSON>('username', String(32), nullable=False, comment='用户名'),
    <PERSON>um<PERSON>('failure_count', Integer, nullable=False, default=0, comment='连续失败次数'),
    <PERSON>um<PERSON>('is_alert', Boolean, nullable=False, default=False, comment='是否告警'),
    <PERSON>umn('created_at', DateTime(), nullable=False, default=datetime.datetime.now, comment='创建时间'),
    Column('updated_at', DateTime(), nullable=False, default=datetime.datetime.now, comment='更新时间'),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    login_failure_records.create(migrate_engine)

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    login_failure_records.drop(migrate_engine)
