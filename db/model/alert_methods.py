# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import DateTime
import datetime

Base = declarative_base()

class AlertMethod(Base):
    """告警方式表 - 扁平化字段设计"""
    __tablename__ = 'alert_methods'

    id = Column('id', Integer, primary_key=True, autoincrement=True)
    name = Column('name', String(255), nullable=False, comment='告警方式名称')
    method_type = Column('method_type', String(50), nullable=False, comment='告警方式类型: email, dingtalk, webhook, sms等')
    description = Column('description', String(500), comment='告警方式描述')

    # 通用字段
    is_enabled = Column('is_enabled', Integer, default=1, comment='是否启用: 1启用, 0禁用')
    is_default = Column('is_default', Integer, default=0, comment='是否为默认告警方式: 1是, 0否')
    order_id = Column('order_id', Integer, nullable=False, default=0, comment='排序ID')
    status = Column('status', String(50), default='active', comment='状态')

    # 邮件告警字段
    smtp_from = Column('smtp_from', String(255), comment='SMTP发件人邮箱')
    smtp_smarthost = Column('smtp_smarthost', String(255), comment='SMTP服务器地址:端口')
    smtp_auth_username = Column('smtp_auth_username', String(255), comment='SMTP认证用户名')
    smtp_auth_password = Column('smtp_auth_password', String(255), comment='SMTP认证密码')
    smtp_require_tls = Column('smtp_require_tls', Integer, default=1, comment='是否需要TLS: 1需要, 0不需要')
    email_to = Column('email_to', Text, comment='邮件接收人，多个用逗号分隔')
    email_subject = Column('email_subject', String(255), comment='邮件主题模板')
    email_body = Column('email_body', Text, comment='邮件内容模板')

    # 钉钉告警字段
    dingtalk_webhook_url = Column('dingtalk_webhook_url', Text, comment='钉钉机器人Webhook地址')
    dingtalk_secret = Column('dingtalk_secret', String(255), comment='钉钉机器人加签密钥')
    dingtalk_at_mobiles = Column('dingtalk_at_mobiles', Text, comment='钉钉@手机号，多个用逗号分隔')
    dingtalk_at_all = Column('dingtalk_at_all', Integer, default=0, comment='是否@所有人: 1是, 0否')
    dingtalk_title = Column('dingtalk_title', String(255), comment='钉钉消息标题模板')
    dingtalk_content = Column('dingtalk_content', Text, comment='钉钉消息内容模板')

    # Webhook告警字段
    webhook_url = Column('webhook_url', Text, comment='Webhook地址')
    webhook_method = Column('webhook_method', String(10), default='POST', comment='HTTP方法: POST, PUT等')
    webhook_headers = Column('webhook_headers', Text, comment='HTTP请求头，JSON格式')
    webhook_body = Column('webhook_body', Text, comment='HTTP请求体模板')
    webhook_timeout = Column('webhook_timeout', Integer, default=10, comment='超时时间(秒)')

    # 短信告警字段
    sms_api_url = Column('sms_api_url', Text, comment='短信API地址')
    sms_api_key = Column('sms_api_key', String(255), comment='短信API密钥')
    sms_api_secret = Column('sms_api_secret', String(255), comment='短信API密钥')
    sms_template_id = Column('sms_template_id', String(100), comment='短信模板ID')
    sms_sign_name = Column('sms_sign_name', String(100), comment='短信签名')
    sms_phone_numbers = Column('sms_phone_numbers', Text, comment='短信接收号码，多个用逗号分隔')
    sms_content = Column('sms_content', String(255), comment='短信内容模板')

    # 企业微信告警字段
    wechat_webhook_url = Column('wechat_webhook_url', Text, comment='企业微信机器人Webhook地址')
    wechat_mentioned_list = Column('wechat_mentioned_list', Text, comment='企业微信@用户列表，多个用逗号分隔')
    wechat_mentioned_mobile_list = Column('wechat_mentioned_mobile_list', Text, comment='企业微信@手机号列表，多个用逗号分隔')
    wechat_title = Column('wechat_title', String(255), comment='企业微信消息标题模板')
    wechat_content = Column('wechat_content', Text, comment='企业微信消息内容模板')

    # 时间字段
    created_at = Column('created_at', DateTime(timezone=True), server_default=func.now())
    updated_at = Column('updated_at', DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AlertMethod(id={self.id}, name='{self.name}', method_type='{self.method_type}')>"

    def to_dict(self):
        created_at_str = self.created_at.isoformat() if self.created_at else None
        updated_at_str = self.updated_at.isoformat() if self.updated_at else None

        return {
            "id": self.id,
            "name": self.name,
            "method_type": self.method_type,
            "description": self.description,
            "is_enabled": self.is_enabled,
            "is_default": self.is_default,
            "order_id": self.order_id,
            "status": self.status,

            # 邮件字段
            "smtp_from": self.smtp_from,
            "smtp_smarthost": self.smtp_smarthost,
            "smtp_auth_username": self.smtp_auth_username,
            "smtp_auth_password": self.smtp_auth_password,
            "smtp_require_tls": self.smtp_require_tls,
            "email_to": self.email_to,
            "email_subject": self.email_subject,
            "email_body": self.email_body,

            # 钉钉字段
            "dingtalk_webhook_url": self.dingtalk_webhook_url,
            "dingtalk_secret": self.dingtalk_secret,
            "dingtalk_at_mobiles": self.dingtalk_at_mobiles,
            "dingtalk_at_all": self.dingtalk_at_all,
            "dingtalk_title": self.dingtalk_title,
            "dingtalk_content": self.dingtalk_content,

            # Webhook字段
            "webhook_url": self.webhook_url,
            "webhook_method": self.webhook_method,
            "webhook_headers": self.webhook_headers,
            "webhook_body": self.webhook_body,
            "webhook_timeout": self.webhook_timeout,

            # 短信字段
            "sms_api_url": self.sms_api_url,
            "sms_api_key": self.sms_api_key,
            "sms_api_secret": self.sms_api_secret,
            "sms_template_id": self.sms_template_id,
            "sms_sign_name": self.sms_sign_name,
            "sms_phone_numbers": self.sms_phone_numbers,
            "sms_content": self.sms_content,

            # 企业微信字段
            "wechat_webhook_url": self.wechat_webhook_url,
            "wechat_mentioned_list": self.wechat_mentioned_list,
            "wechat_mentioned_mobile_list": self.wechat_mentioned_mobile_list,
            "wechat_title": self.wechat_title,
            "wechat_content": self.wechat_content,

            "created_at": created_at_str,
            "updated_at": updated_at_str,
        }
