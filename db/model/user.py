# -*- coding: utf-8 -*-

import uuid
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime

Base = declarative_base()



class Role(Base):
    __tablename__ = 'role'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(16), unique=True)
    display_name = Column(Unicode(16), unique=True)

class User(Base):
    __tablename__ = 'users'
    # id = Column(Integer, primary_key=True, autoincrement=True)
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    username = Column(Unicode(32), unique=True, nullable=False)
    name = Column(Unicode(16))
    role_name = Column(Unicode(16))
    password = Column(Unicode(32))
    session_id = Column(Unicode(32))
    status = Column(Unicode(16), nullable=False, default="on")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True)
    expiredday = Column(DateTime(timezone=True))
    is_first_login = Column(String(16), nullable=True, comment='是否首次登录')
    ukey_status = Column(String(255), nullable=True, comment='ukey状态')
    ukey_write = Column(String(255), nullable=True, comment='ukey写入信息')
    #vms = relationship("Vms", cascade='all,delete-orphan', passive_deletes=True, back_populates='user')


class Group(Base):
    __tablename__ = 'groups'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(16), unique=True)

class UserGroupRole(Base):
    __tablename__ = 'user_group_role'
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    group_id = Column(Integer, ForeignKey('groups.id', ondelete='CASCADE'), nullable=False)
    role_id = Column(Integer, ForeignKey('roles.id', ondelete='CASCADE'), nullable=False)