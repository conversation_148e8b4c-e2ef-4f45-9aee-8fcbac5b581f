# -*- coding: utf-8 -*-
import uuid

from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode, UniqueConstraint, Text, Boolean
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
from db.model.hci.base import BaseModel
from sqlalchemy import inspect
from sqlalchemy.orm import relationship, backref
from db.model.hci.network import Switch, SwitchPorts  # 假设 Switch 类位于这个路径下
from db.model.hci.hardware import HardwareInfo
# 导入中间表定义
from db.model.hci.storage import HostStorageDeviceMapping, HostStoragePoolMapping

class Pool(BaseModel):
    __tablename__ = 'pool'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(32),  nullable=False)
    remark = Column(String(512))
    clusters = relationship("Cluster", back_populates="pool")
    

class Cluster(BaseModel):
    __tablename__ = 'cluster'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    pool_id = Column(String(36), ForeignKey('pool.id'), nullable=False)
    name = Column(String(32),  nullable=False)
    remark = Column(String(512))
    ha_level = Column(Integer, nullable=False, default=0)
    enabled_drs = Column(Integer)
    enable_storage_drs = Column(Integer)
    enable_dmp = Column(Integer)
    cpu_architecture = Column(String(50))
    hosts = relationship("Host", back_populates="cluster")
    pool = relationship("Pool", back_populates="clusters")
    switchs = relationship("Switch", back_populates="cluster")

    
class Host(BaseModel):
    __tablename__ = 'host'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    pool_id = Column(String(36), ForeignKey('pool.id'), nullable=False)
    cluster_id = Column(String(36), ForeignKey('cluster.id'), nullable=False)
    is_supporting = Column(Integer, nullable=True)
    ip = Column(String(32),  nullable=False)
    name = Column(String(32),  nullable=True)
    remark = Column(String(512), nullable=True)
    ssh_user = Column(String(32),  nullable=False)
    ssh_password = Column(String(256),  nullable=False)
    port = Column(String(32), nullable=False, default='22')
    hostname = Column(String(256),  nullable=True)
    host_model = Column(String(256), nullable=True)
    mac = Column(String(32), nullable=True)
    cpu_architecture = Column(String(50),  nullable=True)
    cpu_threads = Column(Integer,  nullable=True)
    cpu_threads_per_core = Column(Integer,  nullable=True)
    cpu_cores_per_socket = Column(Integer,  nullable=True)
    cpu_sockets = Column(Integer,  nullable=True)
    cpu_cores = Column(Integer,  nullable=True)
    cpu_model_name = Column(String(256),  nullable=True)
    cpu_frequency = Column(Integer,  nullable=True)
    cpu_hfrequency = Column(String(30),  nullable=True)
    cpu_virtualization = Column(String(100), nullable=True)
    cpu_virtualization_type = Column(String(100), nullable=True)
    memory = Column(Integer,  nullable=True)
    hmemory = Column(String(30),  nullable=True)
    storage = Column(Integer,  nullable=True)
    hstorage = Column(String(30),  nullable=True)
    is_ha = Column(Integer,  nullable=False, default=0)
    is_maintain = Column(Integer,  nullable=True)
    is_connected = Column(Integer,  nullable=True)
    conn_or_disconn_time = Column(DateTime(timezone=True), default=datetime.datetime.now)
    is_ha_migrated = Column(Integer,  nullable=True, default=0)
    wake_category = Column(Integer,  nullable=True, default=2)
    ipmi_ipaddr = Column(String(32))
    ipmi_user = Column(String(32))
    ipmi_pw = Column(String(256))
    system = Column(String(256))
    uptime = Column(String(256))
    numa_cell = Column(Integer)
    cpu_model_type = Column(String(30))
    bmc = Column(String(256))
    flags = Column(String(4096))
    kv_version = Column(String(32), nullable=True)
    iscsi_initiator = Column(String(256), nullable=True)
    reserve_memory = Column(Integer, nullable=True)
    serial_number = Column(String(256), nullable=True)
    patch_version = Column(String(256), nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    iommu_status =Column(String(32), nullable=True)
    defense_status = Column(Integer, nullable=True)
    role = Column(String(32),  nullable=True)
    domains = relationship("Domain", back_populates="host")
    cluster = relationship("Cluster", back_populates="hosts")
    switchs = relationship("Switch", back_populates="host")
    hardware_info = relationship("HardwareInfo", back_populates="host", uselist=False)
    switch_mappings = relationship("HostSwitchMapping", back_populates="host", cascade="all, delete-orphan")
    storage_devices = relationship(
            "StorageDevice",
            secondary="host_storage_device_mapping",
            back_populates="hosts",
            cascade="all, delete",  # 级联删除中间表记录
            lazy="joined"
    )
    # 添加与 StoragePool 的多对多关系
    storage_pools = relationship(
        "StoragePool",
        secondary="host_storage_pool_mapping",
        back_populates="hosts",
        lazy="joined"
    )

    
class Domain(BaseModel):
    __tablename__ = 'domain'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    pool_id = Column(String(36), ForeignKey('pool.id'), nullable=False)
    cluster_id = Column(String(36), ForeignKey('cluster.id'))
    host_id = Column(String(36), ForeignKey('host.id'), nullable=False)
    name = Column(String(256),  nullable=False)
    os_type = Column(Integer,  nullable=True)
    os_version = Column(String(100))
    remark = Column(String(512))
    status = Column(String(36),  nullable=False)
    is_ha = Column(Integer,  nullable=False, default=0)
    auto_migrate = Column(Integer,  nullable=False, default=0)
    is_losing_contact = Column(Integer,  nullable=True)
    domainname = Column(String(256),  nullable=True)
    uuid = Column(String(36),  nullable=True)
    memory = Column(Integer,  nullable=True)
    hmemory = Column(String(30))
    vcpu = Column(Integer,  nullable=True)
    cpu_arch = Column(String(30),  nullable=True)
    vnc_port = Column(Integer)
    spice_port = Column(Integer)
    bind_ip_list = Column(String(512))
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    operation_time = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True)
    is_persistence = Column(Integer,  nullable=False, default=1)
    secret_id = Column(String(36))
    secret_alg = Column(String(30))
    domain_recycle_id = Column(String(36))
    recycle_or_restore_time = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    safe_status = Column(Integer,  nullable=False, default=0)
    is_hide = Column(Integer,  nullable=False, default=0)
    defense_status = Column(Integer,  nullable=False, default=0)
    is_ignore = Column(Integer,  nullable=False, default=0)
    host = relationship("Host", back_populates="domains")
    # 其他字段...
    port_id = Column(PG_UUID(as_uuid=True), ForeignKey('ports.id'), nullable=False)

    port = relationship("Port", back_populates="domain")

    
class DomainDisk(BaseModel):
    __tablename__ = 'domain_disk'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键')
    domain_id = Column(PG_UUID(as_uuid=True), ForeignKey('domain.id'), nullable=False, comment='(外键)虚拟机')
    host_id = Column(PG_UUID(as_uuid=True), ForeignKey('host.id'), nullable=False, default=uuid.uuid4, comment='(外键)主机')
    storage_pool_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_pool.id'), nullable=True, comment='存储池id')
    storage_pool_type = Column(Integer, nullable=True, comment='存储池的使用类型: 1.FC 2.ISCSI 3.NFS 4.RBD（CEPH） 5.DIR(本地目录) 6.fs(共享存储池)')
    storage_vol_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_volume.id'), nullable=True, comment='(外键)存储卷ID')
    file_name = Column(String(255), nullable=True, comment='文件名称')
    path = Column(String(512), nullable=True, comment='文件路径')
    type_code = Column(String(30), nullable=False, comment='类型(dumpxml中的devices/disk/@type). 可选 file, block. 其他还有 dir, network, volume ...')
    device = Column(String(30), nullable=False, comment='设备(dumpxml中的devices/disk/@device)(没有该属性时,默认为disk). 可选 disk, cdrom, floppy. 其他还有  lun ...')
    dev = Column(String(30), nullable=False, comment='设备名(dumpxml中的devices/disk/target/@dev)(必须符合Linux磁盘命名规则,配合bus字段命名).virtio->vda, ide->hda, scsi/sata/usb->sda, fdc->fda')
    bus = Column(String(30), nullable=False, comment='总线(dumpxml中的devices/disk/target/@bus). 可选 virtio, ide, scsi, sata, usb, fdc')
    qemu_type = Column(String(30), nullable=False, comment='qemu设备类型(dumpxml中的devices/disk/driver/@type)(type=block时一定是raw). 可选 qcow2, raw. 其他还有 qcow, bochs, cloop, cow, iso, qed, vmdk, vpc ...')
    boot_order = Column(Integer, nullable=False, comment='系统引导顺序(dumpxml中的devices/disk/boot/@order). (boot元素缺失时写 int.MaxValue )')
    is_persistence = Column(Integer, nullable=False, default=1, comment='是否持久化')
    shareable = Column(Integer, nullable=False, default=0, comment='是否共享')
    by_path_id = Column(String(64), nullable=True, comment='/dev/disk/by-path下的id')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False)


class DomainInterface(BaseModel):
    __tablename__ = 'domain_interface'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键')
    domain_id = Column(PG_UUID(as_uuid=True), ForeignKey('domain.id'), nullable=False, comment='(外键)虚拟机')
    type_code = Column(String(30), nullable=False, comment='类型(dumpxml中的devices/interface/@type). 固定 bridge. 其他还有 network, direct, user, ethernet, hostdev, mcast, server, udp, vhostuser ...')
    bridge = Column(String(30), nullable=True, comment='虚拟交换机名称(桥接到哪里)(dumpxml中的devices/interface/source/@bridge)(openswitch创建的虚拟交换机名称). type!=bridge时,拿不到就写空字符串')
    mac = Column(String(30), nullable=False, comment='MAC地址(dumpxml中的devices/interface/mac/@address). 用户填写或交由libvirt自动生成')
    ip = Column(String(1024), nullable=True, comment='绑定的ip地址(dumpxml中的devices/interface/ip/@address). 用户填写')
    model = Column(String(30), nullable=True, comment='网络类型(dumpxml中的devices/interface/model/@type). 用户填写')
    driver = Column(String(30), nullable=True, comment='驱动(dumpxml中的devices/interface/driverl/@type)(vhost时叫内核加速). vhost 或其他, 读不到就写null')
    network_strategy = Column(String(256), nullable=True, comment='网络策略名称')
    vf_name = Column(String(32), nullable=True, comment='VF网卡')
    vf_address = Column(String(32), nullable=True, comment='设备编号')
    vlan_id = Column(String(1024), nullable=True, comment='vlan_id')
    mtu = Column(String(6), nullable=True, comment='mtu')
    # 新增外键字段
    switch_port_id = Column(PG_UUID(as_uuid=True), ForeignKey('switchs_ports.id'), nullable=True, comment='关联的交换机端口ID')

    # 可选：增加ORM关系
    switch_port = relationship("SwitchPorts", backref="domain_interfaces")


class DomainXml(BaseModel):
    __tablename__ = 'domain_xml'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键')
    domain_id = Column(PG_UUID(as_uuid=True), ForeignKey('domain.id'), nullable=False, comment='(外键)虚拟机ID')
    xml_content = Column(Text, nullable=False, comment='虚拟机XML配置内容')
    xml_type = Column(String(50), nullable=False, default='domain', comment='XML类型：domain, storage等')
    version = Column(Integer, nullable=False, default=1, comment='XML版本号')
    is_active = Column(Boolean, nullable=False, default=True, comment='是否为当前活跃版本')
    description = Column(String(512), nullable=True, comment='XML配置描述')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间')
    created_by = Column(String(100), nullable=True, comment='创建者')

    # # 关系
    # domain = relationship("Domain", backref="xml_configs")