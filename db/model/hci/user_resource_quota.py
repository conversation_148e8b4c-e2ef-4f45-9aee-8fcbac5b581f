from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, UniqueConstraint, Boolean
from sqlalchemy.ext.declarative import declarative_base
import datetime

from db.model.hci.base import BaseModel


class User(BaseModel):
    __tablename__ = 'users'
    # id = Column(Integer, primary_key=True, autoincrement=True)
    id = Column(String(36), primary_key=True, nullable=False, comment='主键')
    username = Column(String(32), unique=True, nullable=False)
    name = Column(String(16))
    role_name = Column(String(16))
    password = Column(String(32))
    session_id = Column(String(32))
    status = Column(String(16), nullable=False, default="on")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True)
    expiredday = Column(DateTime(timezone=True))
    is_first_login = Column(String(16), nullable=True, comment='是否首次登录')
    ukey_status = Column(String(255), nullable=True, comment='ukey状态')
    ukey_write = Column(String(255), nullable=True, comment='ukey写入信息')


class UserHostAssignment(BaseModel):
    __tablename__ = 'user_host_assignments'
    id = Column(String(36), primary_key=True, nullable=False, comment='主键')
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, comment='用户ID')
    host_id = Column(String(36), ForeignKey('host.id', ondelete='CASCADE'), nullable=False, comment='主机ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间')
    __table_args__ = (UniqueConstraint('user_id', 'host_id', name='uniq_user_host'),)

class UserClusterAssignment(BaseModel):
    __tablename__ = 'user_cluster_assignments'
    id = Column(String(36), primary_key=True, nullable=False, comment='主键')
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, comment='用户ID')
    cluster_id = Column(String(36), ForeignKey('cluster.id', ondelete='CASCADE'), nullable=False, comment='集群ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间')
    __table_args__ = (UniqueConstraint('user_id', 'cluster_id', name='uniq_user_cluster'),)

class UserVmAssignment(BaseModel):
    __tablename__ = 'user_vm_assignments'
    id = Column(String(36), primary_key=True, nullable=False, comment='主键')
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, comment='用户ID')
    vm_id = Column(String(36), ForeignKey('domain.id', ondelete='CASCADE'), nullable=False, comment='虚拟机ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间')
    __table_args__ = (UniqueConstraint('user_id', 'vm_id', name='uniq_user_vm'),)

class UserStoragePool(BaseModel):
    __tablename__ = 'user_storage_pools'
    id = Column(String(36), primary_key=True, nullable=False, comment='主键')
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, comment='用户ID')
    pool_id = Column(String(36), ForeignKey('storage_pool.id', ondelete='CASCADE'), nullable=False, comment='存储池ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False, comment='分配时间')
    __table_args__ = (UniqueConstraint('user_id', 'pool_id', name='uniq_user_pool'),)

class UserQuota(BaseModel):
    __tablename__ = 'user_quotas'
    id = Column(String(36), primary_key=True, nullable=False, comment='主键')
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True, comment='用户ID')
    cpu_limit = Column(Integer, nullable=False, comment='CPU核心数上限')
    cpu_used = Column(Integer, default=0, comment='已使用的CPU核心数')
    memory_limit = Column(Integer, nullable=False, comment='内存上限(MB)')
    memory_used = Column(Integer, default=0, comment='已使用的内存(MB)')
    storage_limit = Column(Integer, nullable=False, comment='存储上限(GB)')
    storage_used = Column(Integer, default=0, comment='已使用的存储(GB)')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False)
