# -*- coding: utf-8 -*-
import uuid
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, BigInteger, VARCHAR, DateTime, Text, Boolean, \
    Float
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import datetime
from db.model.hci.base import BaseModel
# from db.model.hci.compute import Host


class Dictionary(BaseModel):
    __tablename__ = 'dictionary'

    id = Column(Integer, primary_key=True)
    type_code = Column(String, nullable=False)  # 用于区分字典类型，例如 'storage'，'network'
    name = Column(String, nullable=False)
    code = Column(String, nullable=False, unique=True)
    order = Column(Integer, nullable=False)


class StorageDevice(BaseModel):
    __tablename__ = 'storage_device'
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), unique=True, nullable=False,
                comment='主键')
    device_type = Column(String(64), nullable=False,
                         comment='存储的类型: 1 FC_SAN， 2 IP_SAN，3 NFS，4 RBD（CEPH） 5.本地目录')
    device_name = Column(String(100), nullable=False, comment='存储设备名称(区分大小写)')
    model = Column(String(100), nullable=False, comment='型号')
    vendor = Column(String(100), nullable=False, comment='厂家')
    total_capacity = Column(BigInteger, nullable=True, comment='存储总容量,单位:字节(byte)')
    used_capacity = Column(BigInteger, nullable=True, comment='已使用存储总容量,单位:字节(byte)')
    ip_mgmt = Column(String(255), nullable=True, comment='管理ip集合(ip间用逗号隔开),如*************,*************')
    last_scn_time = Column(DateTime(timezone=True), nullable=True, comment='最后扫描的时间')
    username = Column(String(100), nullable=True, comment='用户名')
    password = Column(String(255), nullable=True, comment='密码(应加密存储)')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        comment='更新时间')
    remark = Column(String(255), nullable=True, comment='备注')
    # Relationships
    targets = relationship("IPSANTarget", back_populates="storage_device", cascade="all, delete-orphan")
    storage_pools = relationship("StoragePool", back_populates="storage_device")
    hosts = relationship(
        "Host",
        secondary="host_storage_device_mapping",
        back_populates="storage_devices",
        cascade="all, delete",  # 级联删除中间表记录
        lazy="joined"
    )


class StoragePool(BaseModel):
    __tablename__ = 'storage_pool'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    storage_device_id = Column(PG_UUID(as_uuid=True), ForeignKey("storage_device.id"), nullable=False)
    storage_local_dir = Column(String(100), nullable=True)
    type_code = Column(String(100), nullable=True, default="local")
    type_code_display = Column(String(100), nullable=True, default="本地存储")
    is_nova_libvirt_docker = Column(String(16), nullable=True, default="no")
    outside_prefix = Column(String(100), nullable=True)
    inside_prefix = Column(String(100), nullable=True)
    use_type = Column(Integer, nullable=True, default=0)
    status = Column(Integer, nullable=True, default=0)
    capacity = Column(BigInteger, nullable=True, default=0)
    available = Column(BigInteger, nullable=True, default=0)
    allocation = Column(BigInteger, nullable=True, default=0)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)
    remark = Column(String(512))
    storage_volumes = relationship("StorageVolume", back_populates="storage_pool")
    storage_device = relationship("StorageDevice", back_populates="storage_pools")
    hosts = relationship(
        "Host",
        secondary="host_storage_pool_mapping",
        back_populates="storage_pools",
        lazy="joined"
    )

    # def to_dict(self):
    #     return {
    #         'id': str(self.id),
    #         'name': self.name,
    #         'storage_device_id': str(self.storage_device_id) if self.storage_device_id else None,
    #         'storage_local_dir': self.storage_local_dir,
    #         'type': self.type,
    #         'use_type': self.use_type,
    #         'status': self.status,
    #         'capacity': self.capacity,
    #         'available': self.available,
    #         'allocation': self.allocation,
    #         'time': self.time.isoformat() if self.time else None,
    #         'remark': self.remark,
    #         'volumes': [volume.to_dict() for volume in self.volumes]
    #     }


class StorageVolume(BaseModel):
    __tablename__ = 'storage_volume'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    storage_pool_id = Column(PG_UUID(as_uuid=True), ForeignKey("storage_pool.id"), nullable=False)
    name = Column(String(255), nullable=False)
    protocol_type = Column(String(32), nullable=False)  # 添加协议类型字段
    volume_type = Column(String(16), nullable=True)     # 添加卷类型字段
    # type_code = Column(String(100), nullable=True)
    join_type = Column(Integer, nullable=True)
    path = Column(String(512), nullable=True)
    encrypt = Column(Integer, nullable=True)
    status = Column(Integer, nullable=False, default=0)
    capacity = Column(BigInteger, nullable=True)
    allocation = Column(BigInteger, nullable=True)
    preallocation = Column(BigInteger, nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)
    remark = Column(String(512))
    storage_pool = relationship("StoragePool", back_populates="storage_volumes")

    # def to_dict(self):
    #     return {
    #         'id': str(self.id),
    #         'storage_pool_id': str(self.storage_pool_id) if self.storage_pool_id else None,
    #         'name': self.name,
    #         'type': self.type,
    #         'join_type': self.join_type,
    #         'path': self.path,
    #         'encrypt': self.encrypt,
    #         'status': self.status,
    #         'capacity': self.capacity,
    #         'allocation': self.allocation,
    #         'preallocation': self.preallocation,
    #         'time': self.time.isoformat() if self.time else None,
    #         'remark': self.remark
    #     }


class IPSANTarget(BaseModel):
    __tablename__ = 'ip_san_target'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False,
                       comment='目标主键')
    device_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_device.id', ondelete="CASCADE"), nullable=False,
                       comment='设备ID')
    target_name = Column(String(255), nullable=False, comment='目标名称')
    target_alias = Column(String(100), nullable=True, comment='目标别名')
    ip_address = Column(String(50), nullable=False, comment='IP地址')
    port = Column(Integer, default=3260, nullable=True, comment='端口号')
    authentication_enabled = Column(Boolean, default=False, nullable=True, comment='是否启用认证')
    username = Column(String(100), nullable=True, comment='用户名')
    password = Column(String(255), nullable=True, comment='密码(应加密存储)')
    status = Column(String(16), nullable=False, comment='状态: active, inactive, error')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True, comment='更新时间')

    # Relationships
    storage_device = relationship("StorageDevice", back_populates="targets")

    ip_san_devices = relationship("IPSANDevice", back_populates="target", cascade="all, delete-orphan")

# 从010_add_ip_san.py添加
class IPSANDevice(BaseModel):
    __tablename__ = 'ip_san_device'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False,
                comment='IP SAN 设备主键')
    ip_san_target_id = Column(PG_UUID(as_uuid=True), ForeignKey('ip_san_target.id', ondelete="CASCADE"),
                              nullable=False, comment='target ID')
    iscsi_iqn = Column(String(255), nullable=True, comment='iSCSI Qualified Name')
    authentication_type = Column(String(16), default='none', nullable=True, comment='认证类型: none, CHAP, mutual CHAP')
    target_count = Column(Integer, default=0, nullable=True, comment='目标数量')
    max_sessions = Column(Integer, nullable=True, comment='最大会话数')
    network_type = Column(String(50), nullable=True, comment='网络类型，例如 10GbE, 25GbE')
    use_jumbo_frames = Column(Boolean, default=False, nullable=True, comment='是否使用巨型帧')
    status = Column(String(16), nullable=True, comment='状态')
    mount_type = Column(String(16), nullable=True, comment='挂载类型')
    mount_point = Column(String(255), nullable=True, comment='挂载点')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True, comment='更新时间')

    # LUN 相关信息
    lun_number = Column(String(16), nullable=True, comment='逻辑单元号 (LUN)')
    device_path = Column(String(255), nullable=True, comment='设备路径，例如 /dev/sda')
    scsi_address = Column(String(64), nullable=True, comment='SCSI 地址，例如 2:0:0:0')
    vendor = Column(String(64), nullable=True, comment='设备厂商，例如 FreeNAS')
    model = Column(String(64), nullable=True, comment='设备型号，例如 iSCSI Disk')
    revision = Column(String(16), nullable=True, comment='固件版本，例如 0123')
    state = Column(String(16), nullable=True, comment='设备状态，例如 running')
    fs_type = Column(String(32), nullable=True, comment='文件系统类型，例如 ext4, xfs')
    mounted_nodes = Column(String(255), nullable=True, comment='挂载节点，JSON 格式存储多个节点')
    host_number = Column(String(16), nullable=True, comment='主机适配器编号')
    channel = Column(String(16), nullable=True, comment='通道编号')
    capacity = Column(String(64), nullable=True, comment='容量大小，例如 100GB')
    lun_status = Column(String(16), nullable=True, comment='LUN 状态，例如 online, offline')

    # Relationships
    target = relationship("IPSANTarget", back_populates="ip_san_devices")



# 从011_add_fc_san.py添加
class FCSANDevice(BaseModel):
    __tablename__ = 'fc_san_device'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    device_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_device.id'), nullable=False, unique=True)
    wwn = Column(String(50), nullable=False)
    fc_switch_count = Column(Integer, default=0, nullable=True)
    zone_count = Column(Integer, default=0, nullable=True)
    max_speed = Column(String(20), nullable=True)
    multipathing_type = Column(String(50), nullable=True)
    initiator_count = Column(Integer, default=0, nullable=True)
    target_count = Column(Integer, default=0, nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    storage_device = relationship("StorageDevice")
    ports = relationship("FCSANPort", back_populates="fc_device")


class FCSANPort(BaseModel):
    __tablename__ = 'fc_san_port'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    fc_device_id = Column(PG_UUID(as_uuid=True), ForeignKey('fc_san_device.id'), nullable=False)
    port_wwn = Column(String(50), nullable=False)
    port_type = Column(String(16), nullable=False)
    port_speed = Column(String(20), nullable=True)
    connected_to = Column(String(100), nullable=True)
    status = Column(String(16), nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    fc_device = relationship("FCSANDevice", back_populates="ports")


# 从012_add_nas.py添加
class NASDevice(BaseModel):
    __tablename__ = 'nas_device'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    device_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_device.id'), nullable=False, unique=True)
    hostname = Column(String(100), nullable=True)
    ip_address = Column(String(50), nullable=False)
    protocols = Column(String(100), nullable=True)
    authentication_type = Column(String(50), nullable=True)
    domain_name = Column(String(100), nullable=True)
    has_snapshots = Column(Boolean, default=False, nullable=True)
    quota_enabled = Column(Boolean, default=False, nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    storage_device = relationship("StorageDevice")
    shares = relationship("NASShare", back_populates="nas_device")


class NASShare(BaseModel):
    __tablename__ = 'nas_share'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    nas_device_id = Column(PG_UUID(as_uuid=True), ForeignKey('nas_device.id'), nullable=False)
    share_name = Column(String(255), nullable=False)
    share_path = Column(String(255), nullable=False)
    protocol = Column(String(10), nullable=False)
    access_rights = Column(String(100), nullable=True)
    client_list = Column(String(100), nullable=True)
    quota_limit = Column(BigInteger, nullable=True)
    status = Column(String(10), nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    nas_device = relationship("NASDevice", back_populates="shares")


# 从013_add_ceph.py添加
class CephCluster(BaseModel):
    __tablename__ = 'ceph_cluster'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    device_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_device.id'), nullable=False, unique=True)
    cluster_name = Column(String(100), nullable=False)
    # 新增字段 - 集群标识和管理员信息
    cluster_fsid = Column(String(100), nullable=False, comment='集群唯一标识符')
    admin_username = Column(String(100), nullable=False, default='admin', comment='管理员用户名')
    admin_keyring = Column(Text, nullable=False, comment='管理员密钥')
    # 节点数量信息
    mon_count = Column(Integer, nullable=True)
    osd_count = Column(Integer, nullable=True)
    mgr_count = Column(Integer, nullable=True)
    mds_count = Column(Integer, nullable=True, default=0, comment='MDS节点数量')
    # 集群配置信息
    crush_rule = Column(String(50), nullable=True)
    pg_count = Column(Integer, nullable=True)
    cluster_version = Column(String(50), nullable=True)
    health_status = Column(String(50), nullable=True)
    # 容量信息
    total_capacity = Column(BigInteger, nullable=True, comment='集群总容量(字节)')
    used_capacity = Column(BigInteger, nullable=True, comment='已使用容量(字节)')
    available_capacity = Column(BigInteger, nullable=True, comment='可用容量(字节)')
    # 时间信息
    last_sync_time = Column(DateTime(timezone=True), nullable=True, comment='最后同步时间')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    storage_device = relationship("StorageDevice")
    pools = relationship("CephPool", back_populates="ceph_cluster")
    osds = relationship("CephOSD", back_populates="ceph_cluster")


class CephPool(BaseModel):
    __tablename__ = 'ceph_pool'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    pool_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_pool.id'), nullable=False, unique=True)
    # 重命名外键字段
    ceph_cluster_id = Column(PG_UUID(as_uuid=True), ForeignKey('ceph_cluster.id'), nullable=False)
    # 新增 Ceph 池标识信息
    ceph_pool_name = Column(String(100), nullable=False, comment='Ceph中的池名称')
    ceph_pool_id = Column(Integer, nullable=False, comment='Ceph中的池ID')
    # 池访问认证信息
    pool_username = Column(String(100), nullable=False, comment='访问此池的用户名')
    pool_keyring = Column(Text, nullable=False, comment='访问此池的密钥')
    # 池配置信息
    pool_type = Column(String(20), nullable=False)
    replica_count = Column(Integer, default=3, nullable=True)
    min_size = Column(Integer, default=2, nullable=True, comment='最小副本数')
    ec_profile = Column(String(100), nullable=True)
    pg_count = Column(Integer, nullable=True)
    pgp_count = Column(Integer, nullable=True, comment='PGP数量')
    crush_rule_name = Column(String(100), nullable=True, comment='CRUSH规则名称')
    application_type = Column(String(50), nullable=True)
    # 压缩配置
    compression_enabled = Column(Boolean, default=False, nullable=True)
    compression_algorithm = Column(String(50), nullable=True)
    # 配额设置
    quota_max_objects = Column(BigInteger, nullable=True, comment='最大对象数量配额')
    quota_max_bytes = Column(BigInteger, nullable=True, comment='最大字节数配额')
    # 统计信息
    objects_count = Column(BigInteger, nullable=True, default=0, comment='对象数量')
    stored_bytes = Column(BigInteger, nullable=True, default=0, comment='存储字节数')
    # 时间信息
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    storage_pool = relationship("StoragePool")
    ceph_cluster = relationship("CephCluster", back_populates="pools")


class CephOSD(BaseModel):
    __tablename__ = 'ceph_osd'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    # 重命名外键字段
    ceph_cluster_id = Column(PG_UUID(as_uuid=True), ForeignKey('ceph_cluster.id'), nullable=False)
    # OSD 基本信息
    osd_number = Column(Integer, nullable=False)
    host_server = Column(String(100), nullable=True)
    device_path = Column(String(255), nullable=True)
    weight = Column(Float, nullable=True)
    status = Column(String(50), nullable=True)
    class_type = Column(String(50), nullable=True, name='class')
    up = Column(Boolean, default=True, nullable=True)
    in_status = Column(Boolean, default=True, nullable=True, name='in')
    # 新增容量信息
    total_capacity = Column(BigInteger, nullable=True, comment='OSD总容量')
    used_capacity = Column(BigInteger, nullable=True, comment='已使用容量')
    available_capacity = Column(BigInteger, nullable=True, comment='可用容量')
    # 时间信息
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        nullable=True)

    ceph_cluster = relationship("CephCluster", back_populates="osds")


# 从014_add_host_volume_mapping.py添加
class ClientHost(BaseModel):
    __tablename__ = 'client_host'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    hostname = Column(String(100), nullable=False)
    ip_address = Column(String(50), nullable=True)
    os_type = Column(String(50), nullable=True)
    fc_wwn = Column(Text, nullable=True)
    iscsi_iqn = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)

    volume_mappings = relationship("HostVolumeMapping", back_populates="client_host")


class HostVolumeMapping(BaseModel):
    __tablename__ = 'host_volume_mapping'
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    host_id = Column(PG_UUID(as_uuid=True), ForeignKey('client_host.id'), nullable=False)
    volume_id = Column(PG_UUID(as_uuid=True), ForeignKey('storage_volume.id'), nullable=False)
    lun_id = Column(Integer, nullable=True)
    read_only = Column(Boolean, default=False, nullable=True)
    status = Column(String(50), nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=True)

    client_host = relationship("ClientHost", back_populates="volume_mappings")
    storage_volume = relationship("StorageVolume")


class HostStorageDeviceMapping(BaseModel):
    __tablename__ = 'host_storage_device_mapping'

    id = Column(String(36), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键')
    host_id = Column(String(36), ForeignKey('host.id', ondelete="CASCADE"), nullable=False, comment='主机ID')
    storage_device_id = Column(String(36), ForeignKey('storage_device.id', ondelete="CASCADE"), nullable=False, comment='存储设备ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        comment='更新时间')
    remark = Column(String(255), nullable=True, comment='备注')

    # 定义与 Host 和 StorageDevice 的关系
    # host = relationship("Host", back_populates="storage_devices")
    # storage_device = relationship("StorageDevice", back_populates="hosts")


class HostStoragePoolMapping(BaseModel):
    __tablename__ = 'host_storage_pool_mapping'

    id = Column(String(36), primary_key=True, default=uuid.uuid4, unique=True, nullable=False, comment='主键')
    host_id = Column(String(36), ForeignKey('host.id'), nullable=False, comment='主机ID')
    storage_pool_id = Column(String(36), ForeignKey('storage_pool.id'), nullable=False, comment='存储池ID')
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now, comment='创建时间')
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now, onupdate=datetime.datetime.now,
                        comment='更新时间')
    remark = Column(String(255), nullable=True, comment='备注')

    # 定义与 Host 和 StoragePool 的关系
    # host = relationship("Host", back_populates="storage_pools")
    # storage_pool = relationship("StoragePool", back_populates="hosts")


class StoragePoolGroup(BaseModel):
    __tablename__ = 'storage_pool_group'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='组ID')
    name = Column(String(64), nullable=False, comment='组名称')
    pid = Column(Integer, nullable=False, default=0, comment='父组ID，0为顶级组')
    remark = Column(String(64), nullable=False, comment='备注')
    created_at = Column(DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')

    # 关系：一个组可以有多个池
    pools = relationship("StoragePoolGroupMapping", back_populates="group", cascade="all, delete-orphan")

class StoragePoolGroupMapping(BaseModel):
    __tablename__ = 'storage_pool_group_mapping'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键')
    storage_pool_group_id = Column(Integer, ForeignKey('storage_pool_group.id'), nullable=False, comment='组ID')
    pool_id = Column(String(36), nullable=False, comment='存储池ID（uuid）')
    created_at = Column(DateTime, default=datetime.datetime.now, comment='创建时间')

    # 关系：映射到组
    group = relationship("StoragePoolGroup", back_populates="pools")