#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ceph RBD 卷管理工具类
支持创建、删除、扩容、克隆等操作
"""

import subprocess
import json
import os
import tempfile
import logging
from typing import Dict, List, Optional, Tuple


class CephRBDManager:
    """Ceph RBD 卷管理器"""

    def __init__(self, cluster_ips: List[str], username: str, keyring: str, pool_name: str):
        """
        初始化 Ceph RBD 管理器

        Args:
            cluster_ips: Ceph 集群 Monitor 节点 IP 列表，格式: ["***********:6789", "***********:6789"]
            username: Ceph 用户名
            keyring: Ceph 用户密钥
            pool_name: RBD 池名称
        """
        self.cluster_ips = cluster_ips
        self.username = username
        self.keyring = keyring
        self.pool_name = pool_name
        self.logger = logging.getLogger(__name__)

        # 创建临时密钥文件
        self.keyring_file = self._create_keyring_file()

    def _create_keyring_file(self) -> str:
        """创建临时密钥文件"""
        try:
            # 创建临时文件
            fd, keyring_path = tempfile.mkstemp(suffix='.keyring', prefix='ceph_')

            # 写入密钥内容
            keyring_content = f"""[client.{self.username}]
    key = {self.keyring}
"""
            with os.fdopen(fd, 'w') as f:
                f.write(keyring_content)

            # 设置文件权限
            os.chmod(keyring_path, 0o600)

            self.logger.info(f"创建临时密钥文件: {keyring_path}")
            return keyring_path

        except Exception as e:
            self.logger.error(f"创建密钥文件失败: {str(e)}")
            raise

    def _get_base_cmd(self) -> List[str]:
        """获取基础 RBD 命令参数"""
        monitors = ",".join(self.cluster_ips)
        return [
            "rbd",
            "--id", self.username,
            "--key", self.keyring,  # 直接使用key而不是keyring文件
            "--mon_host", monitors,  # 使用--mon_host而不是-m
            "--conf", "/dev/null"  # 不使用默认配置文件
        ]

    def _run_command(self, cmd: List[str], check_output: bool = True) -> Tuple[bool, str]:
        """
        执行命令

        Args:
            cmd: 命令列表
            check_output: 是否检查输出

        Returns:
            (success, output): 成功标志和输出内容
        """
        try:
            self.logger.info(f"执行命令: {' '.join(cmd)}")

            if check_output:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    check=True,
                    timeout=300  # 5分钟超时
                )
                return True, result.stdout.strip()
            else:
                subprocess.run(cmd, check=True, timeout=300)
                return True, ""

        except subprocess.CalledProcessError as e:
            error_msg = f"命令执行失败: {e.stderr if e.stderr else str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
        except subprocess.TimeoutExpired:
            error_msg = "命令执行超时"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"命令执行异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_volume(self, volume_name: str, size_gb: int, image_format: str = "2") -> Tuple[bool, str]:
        """
        创建 RBD 卷

        Args:
            volume_name: 卷名称
            size_gb: 卷大小（GB）
            image_format: 镜像格式，默认为 "2"

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            cmd = self._get_base_cmd() + [
                "create",
                f"{self.pool_name}/{volume_name}",
                "--size", f"{size_gb}G",
                "--image-format", image_format
            ]

            success, output = self._run_command(cmd, check_output=False)

            if success:
                message = f"成功创建 RBD 卷: {self.pool_name}/{volume_name} ({size_gb}GB)"
                self.logger.info(message)
                return True, message
            else:
                return False, f"创建 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"创建 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def delete_volume(self, volume_name: str) -> Tuple[bool, str]:
        """
        删除 RBD 卷

        Args:
            volume_name: 卷名称

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            cmd = self._get_base_cmd() + [
                "rm",
                f"{self.pool_name}/{volume_name}"
            ]

            success, output = self._run_command(cmd, check_output=False)

            if success:
                message = f"成功删除 RBD 卷: {self.pool_name}/{volume_name}"
                self.logger.info(message)
                return True, message
            else:
                return False, f"删除 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"删除 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def resize_volume(self, volume_name: str, new_size_gb: int) -> Tuple[bool, str]:
        """
        扩容 RBD 卷

        Args:
            volume_name: 卷名称
            new_size_gb: 新的卷大小（GB）

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            cmd = self._get_base_cmd() + [
                "resize",
                f"{self.pool_name}/{volume_name}",
                "--size", f"{new_size_gb}G"
            ]

            success, output = self._run_command(cmd, check_output=False)

            if success:
                message = f"成功扩容 RBD 卷: {self.pool_name}/{volume_name} 到 {new_size_gb}GB"
                self.logger.info(message)
                return True, message
            else:
                return False, f"扩容 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"扩容 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def clone_volume(self, source_volume: str, dest_volume: str,
                    source_pool: Optional[str] = None) -> Tuple[bool, str]:
        """
        克隆 RBD 卷

        Args:
            source_volume: 源卷名称
            dest_volume: 目标卷名称
            source_pool: 源池名称，如果为空则使用当前池

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            if source_pool is None:
                source_pool = self.pool_name

            # 首先创建快照
            snapshot_name = f"{dest_volume}_clone_snap"
            snap_cmd = self._get_base_cmd() + [
                "snap", "create",
                f"{source_pool}/{source_volume}@{snapshot_name}"
            ]

            success, output = self._run_command(snap_cmd, check_output=False)
            if not success:
                return False, f"创建快照失败: {output}"

            # 保护快照
            protect_cmd = self._get_base_cmd() + [
                "snap", "protect",
                f"{source_pool}/{source_volume}@{snapshot_name}"
            ]

            success, output = self._run_command(protect_cmd, check_output=False)
            if not success:
                # 清理快照
                self._cleanup_snapshot(source_pool, source_volume, snapshot_name)
                return False, f"保护快照失败: {output}"

            # 克隆卷
            clone_cmd = self._get_base_cmd() + [
                "clone",
                f"{source_pool}/{source_volume}@{snapshot_name}",
                f"{self.pool_name}/{dest_volume}"
            ]

            success, output = self._run_command(clone_cmd, check_output=False)
            if not success:
                # 清理快照
                self._cleanup_snapshot(source_pool, source_volume, snapshot_name)
                return False, f"克隆卷失败: {output}"

            # 扁平化克隆（可选，使克隆独立于父卷）
            flatten_cmd = self._get_base_cmd() + [
                "flatten",
                f"{self.pool_name}/{dest_volume}"
            ]

            success, output = self._run_command(flatten_cmd, check_output=False)
            if not success:
                self.logger.warning(f"扁平化克隆失败，但克隆已创建: {output}")

            # 清理快照
            self._cleanup_snapshot(source_pool, source_volume, snapshot_name)

            message = f"成功克隆 RBD 卷: {source_pool}/{source_volume} -> {self.pool_name}/{dest_volume}"
            self.logger.info(message)
            return True, message

        except Exception as e:
            error_msg = f"克隆 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def _cleanup_snapshot(self, pool_name: str, volume_name: str, snapshot_name: str):
        """清理快照"""
        try:
            # 取消保护
            unprotect_cmd = self._get_base_cmd() + [
                "snap", "unprotect",
                f"{pool_name}/{volume_name}@{snapshot_name}"
            ]
            self._run_command(unprotect_cmd, check_output=False)

            # 删除快照
            rm_snap_cmd = self._get_base_cmd() + [
                "snap", "rm",
                f"{pool_name}/{volume_name}@{snapshot_name}"
            ]
            self._run_command(rm_snap_cmd, check_output=False)

        except Exception as e:
            self.logger.warning(f"清理快照失败: {str(e)}")

    def rename_volume(self, old_name: str, new_name: str) -> Tuple[bool, str]:
        """
        重命名 RBD 卷

        Args:
            old_name: 旧卷名称
            new_name: 新卷名称

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            cmd = self._get_base_cmd() + [
                "mv",
                f"{self.pool_name}/{old_name}",
                f"{self.pool_name}/{new_name}"
            ]

            success, output = self._run_command(cmd, check_output=False)

            if success:
                message = f"成功重命名 RBD 卷: {old_name} -> {new_name}"
                self.logger.info(message)
                return True, message
            else:
                return False, f"重命名 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"重命名 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_volume_info(self, volume_name: str) -> Tuple[bool, Dict]:
        """
        获取 RBD 卷信息

        Args:
            volume_name: 卷名称

        Returns:
            (success, info): 成功标志和卷信息字典
        """
        try:
            cmd = self._get_base_cmd() + [
                "info",
                f"{self.pool_name}/{volume_name}",
                "--format", "json"
            ]

            success, output = self._run_command(cmd, check_output=True)

            if success:
                info = json.loads(output)
                self.logger.info(f"获取 RBD 卷信息成功: {volume_name}")
                return True, info
            else:
                return False, {"error": f"获取卷信息失败: {output}"}

        except json.JSONDecodeError as e:
            error_msg = f"解析卷信息 JSON 失败: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}
        except Exception as e:
            error_msg = f"获取 RBD 卷信息异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}

    def list_volumes(self) -> Tuple[bool, List[Dict]]:
        """
        列出池中的所有 RBD 卷

        Returns:
            (success, volumes): 成功标志和卷列表
        """
        try:
            cmd = self._get_base_cmd() + [
                "ls",
                self.pool_name,
                "--format", "json"
            ]

            success, output = self._run_command(cmd, check_output=True)

            if success:
                volumes = json.loads(output) if output else []
                self.logger.info(f"列出 RBD 卷成功，共 {len(volumes)} 个卷")
                return True, volumes
            else:
                return False, []

        except json.JSONDecodeError as e:
            error_msg = f"解析卷列表 JSON 失败: {str(e)}"
            self.logger.error(error_msg)
            return False, []
        except Exception as e:
            error_msg = f"列出 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, []

    def get_pool_stats(self) -> Tuple[bool, Dict]:
        """
        获取池统计信息

        Returns:
            (success, stats): 成功标志和统计信息
        """
        try:
            cmd = self._get_base_cmd() + [
                "du",
                self.pool_name,
                "--format", "json"
            ]

            success, output = self._run_command(cmd, check_output=True)

            if success:
                stats = json.loads(output)
                self.logger.info(f"获取池统计信息成功: {self.pool_name}")
                return True, stats
            else:
                return False, {"error": f"获取池统计信息失败: {output}"}

        except json.JSONDecodeError as e:
            error_msg = f"解析池统计信息 JSON 失败: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}
        except Exception as e:
            error_msg = f"获取池统计信息异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}

    def volume_exists(self, volume_name: str) -> bool:
        """
        检查 RBD 卷是否存在

        Args:
            volume_name: 卷名称

        Returns:
            bool: 卷是否存在
        """
        try:
            success, volumes = self.list_volumes()
            if success:
                return volume_name in volumes
            return False
        except Exception as e:
            self.logger.error(f"检查卷是否存在异常: {str(e)}")
            return False

    def __del__(self):
        """析构函数，清理临时文件"""
        try:
            if hasattr(self, 'keyring_file') and os.path.exists(self.keyring_file):
                os.unlink(self.keyring_file)
                self.logger.info(f"清理临时密钥文件: {self.keyring_file}")
        except Exception as e:
            self.logger.warning(f"清理临时文件失败: {str(e)}")


class QemuImgCephManager:
    """使用 qemu-img 操作 Ceph RBD 的管理器

    注意: qemu-img 主要用于创建卷和格式转换，不支持 RBD 的管理操作
    如需删除、重命名、克隆等操作，请使用 CephRBDManager
    """

    def __init__(self, cluster_ips: List[str], username: str, keyring: str, pool_name: str):
        """
        初始化 qemu-img Ceph 管理器

        Args:
            cluster_ips: Ceph 集群 Monitor 节点 IP 列表
            username: Ceph 用户名
            keyring: Ceph 用户密钥
            pool_name: RBD 池名称
        """
        self.cluster_ips = cluster_ips
        self.username = username
        self.keyring = keyring
        self.pool_name = pool_name
        self.logger = logging.getLogger(__name__)

        # qemu-img 不需要密钥文件，直接在 URL 中传递认证信息

    def _get_rbd_url(self, volume_name: str) -> str:
        """
        获取 RBD URL，用于 qemu-img 操作

        Args:
            volume_name: 卷名称

        Returns:
            str: RBD URL
        """
        # 使用分号分隔集群地址，并对冒号进行转义
        monitors = "\\;".join([ip.replace(":", "\\:") for ip in self.cluster_ips])
        return f"rbd:{self.pool_name}/{volume_name}:id={self.username}:key={self.keyring}:mon_host={monitors}"

    def _run_qemu_command(self, cmd: List[str]) -> Tuple[bool, str]:
        """执行 qemu-img 命令"""
        try:
            self.logger.info(f"执行 qemu-img 命令: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                timeout=600  # 10分钟超时
            )

            return True, result.stdout.strip()

        except subprocess.CalledProcessError as e:
            error_msg = f"qemu-img 命令执行失败: {e.stderr if e.stderr else str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
        except subprocess.TimeoutExpired:
            error_msg = "qemu-img 命令执行超时"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"qemu-img 命令执行异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_volume_qemu(self, volume_name: str, size_gb: int,
                          image_format: str = "rawu") -> Tuple[bool, str]:
        """
        使用 qemu-img 创建 RBD 卷

        Args:
            volume_name: 卷名称
            size_gb: 卷大小（GB）
            image_format: 镜像格式，默认为 raw

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            rbd_url = self._get_rbd_url(volume_name)

            cmd = [
                "qemu-img", "create",
                "-f", image_format,
                rbd_url,
                f"{size_gb}G"
            ]

            success, output = self._run_qemu_command(cmd)

            if success:
                message = f"使用 qemu-img 成功创建 RBD 卷: {self.pool_name}/{volume_name} ({size_gb}GB)"
                self.logger.info(message)
                return True, message
            else:
                return False, f"使用 qemu-img 创建 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"使用 qemu-img 创建 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def resize_volume_qemu(self, volume_name: str, new_size_gb: int) -> Tuple[bool, str]:
        """
        使用 qemu-img 扩容 RBD 卷

        注意: qemu-img resize 对 RBD 的支持有限，建议使用原生 rbd resize 命令

        Args:
            volume_name: 卷名称
            new_size_gb: 新的卷大小（GB）

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            rbd_url = self._get_rbd_url(volume_name)

            cmd = [
                "qemu-img", "resize",
                rbd_url,
                f"{new_size_gb}G"
            ]

            success, output = self._run_qemu_command(cmd)

            if success:
                message = f"使用 qemu-img 成功扩容 RBD 卷: {self.pool_name}/{volume_name} 到 {new_size_gb}GB"
                self.logger.info(message)
                return True, message
            else:
                return False, f"使用 qemu-img 扩容 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"使用 qemu-img 扩容 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_volume_info_qemu(self, volume_name: str) -> Tuple[bool, Dict]:
        """
        使用 qemu-img 获取 RBD 卷信息

        Args:
            volume_name: 卷名称

        Returns:
            (success, info): 成功标志和卷信息字典
        """
        try:
            rbd_url = self._get_rbd_url(volume_name)

            cmd = [
                "qemu-img", "info",
                "--output", "json",
                rbd_url
            ]

            success, output = self._run_qemu_command(cmd)

            if success:
                info = json.loads(output)
                self.logger.info(f"使用 qemu-img 获取 RBD 卷信息成功: {volume_name}")
                return True, info
            else:
                return False, {"error": f"使用 qemu-img 获取卷信息失败: {output}"}

        except json.JSONDecodeError as e:
            error_msg = f"解析 qemu-img 输出 JSON 失败: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}
        except Exception as e:
            error_msg = f"使用 qemu-img 获取 RBD 卷信息异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}

    def convert_volume(self, source_volume: str, dest_volume: str,
                      source_format: str = "raw", dest_format: str = "qcow2") -> Tuple[bool, str]:
        """
        使用 qemu-img 转换 RBD 卷格式

        这是 qemu-img 的主要优势之一，可以在不同格式间转换

        Args:
            source_volume: 源卷名称
            dest_volume: 目标卷名称
            source_format: 源格式（RBD 通常是 raw）
            dest_format: 目标格式（如 qcow2）

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            source_url = self._get_rbd_url(source_volume)
            dest_url = self._get_rbd_url(dest_volume)

            cmd = [
                "qemu-img", "convert",
                "-f", source_format,
                "-O", dest_format,
                source_url,
                dest_url
            ]

            success, output = self._run_qemu_command(cmd)

            if success:
                message = f"使用 qemu-img 成功转换 RBD 卷: {source_volume} ({source_format}) -> {dest_volume} ({dest_format})"
                self.logger.info(message)
                return True, message
            else:
                return False, f"使用 qemu-img 转换 RBD 卷失败: {output}"

        except Exception as e:
            error_msg = f"使用 qemu-img 转换 RBD 卷异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg


def create_ceph_manager(cluster_ips: List[str], username: str, keyring: str,
                       pool_name: str, use_qemu: bool = False):
    """
    工厂函数：创建 Ceph 管理器实例

    Args:
        cluster_ips: Ceph 集群 Monitor 节点 IP 列表
        username: Ceph 用户名
        keyring: Ceph 用户密钥
        pool_name: RBD 池名称
        use_qemu: 是否使用 qemu-img，默认使用原生 rbd 命令

    Returns:
        CephRBDManager 或 QemuImgCephManager 实例
    """
    if use_qemu:
        return QemuImgCephManager(cluster_ips, username, keyring, pool_name)
    else:
        return CephRBDManager(cluster_ips, username, keyring, pool_name)


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)

    # 示例配置
    cluster_ips = ["***********:6789", "***********:6789", "***********:6789"]
    username = "admin"
    keyring = "AQBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx=="
    pool_name = "rbd"

    # 使用原生 rbd 命令
    print("=== 使用原生 rbd 命令 ===")
    rbd_manager = create_ceph_manager(cluster_ips, username, keyring, pool_name, use_qemu=False)

    # 创建卷
    success, msg = rbd_manager.create_volume("test_volume", 10)
    print(f"创建卷: {success}, {msg}")

    # 获取卷信息
    success, info = rbd_manager.get_volume_info("test_volume")
    print(f"卷信息: {success}, {info}")

    # 扩容卷
    success, msg = rbd_manager.resize_volume("test_volume", 20)
    print(f"扩容卷: {success}, {msg}")

    # 克隆卷
    success, msg = rbd_manager.clone_volume("test_volume", "test_volume_clone")
    print(f"克隆卷: {success}, {msg}")

    # 列出卷
    success, volumes = rbd_manager.list_volumes()
    print(f"卷列表: {success}, {volumes}")

    # 删除卷
    success, msg = rbd_manager.delete_volume("test_volume_clone")
    print(f"删除克隆卷: {success}, {msg}")

    success, msg = rbd_manager.delete_volume("test_volume")
    print(f"删除原卷: {success}, {msg}")

    print("\n=== 使用 qemu-img 命令 ===")
    qemu_manager = create_ceph_manager(cluster_ips, username, keyring, pool_name, use_qemu=True)

    # 使用 qemu-img 创建卷
    success, msg = qemu_manager.create_volume_qemu("qemu_test_volume", 10)
    print(f"qemu-img 创建卷: {success}, {msg}")

    # 使用 qemu-img 获取卷信息
    success, info = qemu_manager.get_volume_info_qemu("qemu_test_volume")
    print(f"qemu-img 卷信息: {success}, {info}")

    # 使用 qemu-img 扩容卷
    success, msg = qemu_manager.resize_volume_qemu("qemu_test_volume", 20)
    print(f"qemu-img 扩容卷: {success}, {msg}")

    # 清理（需要使用原生 rbd 命令删除）
    success, msg = rbd_manager.delete_volume("qemu_test_volume")
    print(f"删除 qemu 创建的卷: {success}, {msg}")