'''
Created on Mar 1, 2022

@author: maojj
'''
from datetime import datetime, timedelta, timezone

import requests
import json
import settings
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
from prometheus_client.parser import text_string_to_metric_families

from datetime import datetime, timedelta

from api.log.log import CustomLogger

new_logger = CustomLogger()

    
class AlertClient(object):
    
    url = settings.RROMETHEUS_ALERT_URI    
    token = ""
    
    def get_alert_manager_alert(self):
        
        url_path = "/api/v2/alerts?filter=job!=\"ceph\"&silenced=false&inhibited=false&active=true"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)
        
        r = requests.get(url)
        data = json.loads(r.text)
        
        res = []
        for o in data:
            
            active_at = o["startsAt"].rstrip('Z')
            if '.' in active_at:
                # Split the string at the dot, to separate microseconds
                main_time, microseconds = active_at.split('.')
                # Truncate the microseconds to six digits and reassemble the time string
                active_at = f"{main_time}.{microseconds[:6]}"

            # Parse the datetime string into a datetime object, now without 'Z'
            utc_time = datetime.strptime(active_at, "%Y-%m-%dT%H:%M:%S.%f")

            # Set the timezone to UTC
            utc_time = utc_time.replace(tzinfo=timezone.utc)

            # Define the time difference for Eastern time (UTC+8)
            utc_offset = timedelta(hours=8)

            # Convert to Eastern time
            eastern_time = utc_time + utc_offset

            # Format the datetime object into a string including timezone information as '+0800'
            #o["activeAt"] = eastern_time.strftime("%Y-%m-%d %H:%M:%S")
            
            tmp = {
                "summary": o["annotations"]["summary"],
                "fingerprint": o["fingerprint"],
                "alertname": o["labels"]["alertname"],
                'instance': o["labels"].get("instance", ""),
                'severity': o["labels"]["severity"],
                'description':o["annotations"]["description"],
                'activeAt': eastern_time.strftime("%Y-%m-%d %H:%M:%S")
                }
            res.append(tmp)
        return res

    def get_alert_ceph_alert(self):

        url_path = "/api/v2/alerts?filter=job=\"ceph\"&silenced=false&inhibited=false&active=true"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)

        r = requests.get(url)
        data = json.loads(r.text)

        res = []
        for o in data:
            print(o)

            active_at = o["startsAt"].rstrip('Z')
            if '.' in active_at:
                # Split the string at the dot, to separate microseconds
                main_time, microseconds = active_at.split('.')
                # Truncate the microseconds to six digits and reassemble the time string
                active_at = f"{main_time}.{microseconds[:6]}"

            # Parse the datetime string into a datetime object, now without 'Z'
            utc_time = datetime.strptime(active_at, "%Y-%m-%dT%H:%M:%S.%f")

            # Set the timezone to UTC
            utc_time = utc_time.replace(tzinfo=timezone.utc)

            # Define the time difference for Eastern time (UTC+8)
            utc_offset = timedelta(hours=8)

            # Convert to Eastern time
            eastern_time = utc_time + utc_offset

            # Format the datetime object into a string including timezone information as '+0800'
            # o["activeAt"] = eastern_time.strftime("%Y-%m-%d %H:%M:%S")

            tmp = {
                "summary": o["annotations"]["summary"],
                "fingerprint": o["fingerprint"],
                "alertname": o["labels"]["alertname"],
                # 'instance': o["labels"]["instance"],
                'severity': o["labels"]["severity"],
                'description': o["annotations"]["description"],
                'activeAt': eastern_time.strftime("%Y-%m-%d %H:%M:%S")
            }
            res.append(tmp)
        return res

    def get_alert_manager_silences(self):
        url_path = "/api/v2/silences"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)
        
        r = requests.get(url)
        data = json.loads(r.text)        
        
        res = []
        for o in data:
            print(o)
            res.append(o)
        return res
            
    def add_alert_manager_silences(self, name, value):
        """
           忽略告警　默认３天
        """
        role = self.get_cookie('role', "")
        url_path = "/api/v2/silences"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)
        
        starts_at = datetime.utcnow()
        ends_at = starts_at + timedelta(days=3)        

        # Silence details
        silence_data = {
          "matchers": [
            {"name": name, "value": value, "isRegex": False}
          ],
          "startsAt": starts_at.isoformat() + "Z",
          "endsAt": ends_at.isoformat() + "Z",
          "createdBy": "api",
          "comment": "http api"
        }
        
        # Send the request
        response = requests.post(url, data=json.dumps(silence_data), headers={'Content-Type': 'application/json'})
        
        # Check the response
        if response.status_code == 200:
            print("Silence added successfully")

            return {"msg": "ok"}
        else:
            print("Failed to add silence")

            return {"msg": "error"}
             
    
    def filter_fingerprint(self, ids):
        url_path = "/api/v2/alerts?filter=job!=\"ceph\"&silenced=false&inhibited=false&active=true"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)
        
        r = requests.get(url)
        data = json.loads(r.text)
        
        res = []
        for o in data:
            tmp = {
                "summary": o["annotations"]["summary"],
                "fingerprint": o["fingerprint"],
                "alertname": o["labels"]["alertname"],
                'instance': o["labels"].get("instance", ""),
                'severity': o["labels"]["severity"]
                }
            if o["fingerprint"] in ids:
                res.append(tmp)
                
        return res

    
    def add_alert_manager_silences_fingerprint(self, role, ids):
        """
           忽略告警　默认３天
        """
        data = self.filter_fingerprint(ids)
        
        url_path = "/api/v2/silences"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)
        
        starts_at = datetime.utcnow()
        ends_at = starts_at + timedelta(days=3)
              
        for o in data:
            # Silence details
            silence_data = {
              "matchers": [
                {"name": "alertname", "value": o["alertname"], "isRegex": False},
                {"name": "instance", "value": o["instance"], "isRegex": False},
              ],
              "startsAt": starts_at.isoformat() + "Z",
              "endsAt": ends_at.isoformat() + "Z",
              "createdBy": "api",
              "comment": "http api"
            }
            
            # Send the request
            response = requests.post(url, data=json.dumps(silence_data), headers={'Content-Type': 'application/json'})
            
            # Check the response
            if response.status_code == 200:
                print("Silence added successfully")
            else:
                print("Failed to add silence")
        
        return {"msg":"ok"}

    def filter_ceph_fingerprint(self, ids):
        url_path = "/api/v2/alerts?filter=job=\"ceph\"&silenced=false&inhibited=false&active=true"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)

        r = requests.get(url)
        data = json.loads(r.text)

        res = []
        for o in data:
            tmp = {
                "summary": o["annotations"]["summary"],
                "fingerprint": o["fingerprint"],
                "alertname": o["labels"]["alertname"],
                'severity': o["labels"]["severity"]
            }
            if o["fingerprint"] in ids:
                res.append(tmp)

        return res

    def add_alert_ceph_silences_fingerprint(self, ids, role):
        """
           忽略告警　默认３天
        """
        
        data = self.filter_ceph_fingerprint(ids)

        url_path = "/api/v2/silences"
        url = "%s%s" % (settings.ALERTMANAGER_URI, url_path)

        starts_at = datetime.utcnow()
        ends_at = starts_at + timedelta(days=3)

        for o in data:
            # Silence details
            silence_data = {
                "matchers": [
                    {"name": "alertname", "value": o["alertname"], "isRegex": False},
                ],
                "startsAt": starts_at.isoformat() + "Z",
                "endsAt": ends_at.isoformat() + "Z",
                "createdBy": "api",
                "comment": "http api"
            }

            # Send the request
            response = requests.post(url, data=json.dumps(silence_data), headers={'Content-Type': 'application/json'})

            # Check the response
            if response.status_code == 200:
                print("Silence added successfully")
            else:
                print("Failed to add silence")

        return {"msg": "ok"}

    def get_alert_list(self):
        
        r = requests.get(self.url)
        data = json.loads(r.text)
        res = []
        for o in data["data"]["alerts"]:
            #if ("labels" in o and "job" in o["labels"]):
                # if o["labels"]["job"] != "openstackwindows" or o["labels"]["job"] != "openstacklinux" or o["labels"]["job"] != "node-exporter":
                #     continue
            # Remove the 'Z' and truncate the microseconds to six digits
            active_at = o["activeAt"].rstrip('Z')
            if '.' in active_at:
                # Split the string at the dot, to separate microseconds
                main_time, microseconds = active_at.split('.')
                # Truncate the microseconds to six digits and reassemble the time string
                active_at = f"{main_time}.{microseconds[:6]}"

            # Parse the datetime string into a datetime object, now without 'Z'
            utc_time = datetime.strptime(active_at, "%Y-%m-%dT%H:%M:%S.%f")

            # Set the timezone to UTC
            utc_time = utc_time.replace(tzinfo=timezone.utc)

            # Define the time difference for Eastern time (UTC+8)
            utc_offset = timedelta(hours=8)

            # Convert to Eastern time
            eastern_time = utc_time + utc_offset

            # Format the datetime object into a string including timezone information as '+0800'
            o["activeAt"] = eastern_time.strftime("%Y-%m-%d %H:%M:%S")
            res.append(o)

            sorted_data = sorted(res, key=lambda x: x["activeAt"], reverse=True)
            res = sorted_data
        return res
        
#alert = AlertClient()
#alert.add_alert_manager_silences("summary", "节点失联 (instance 192.168.214.102:9105)")
#alert.get_alert_manager_silences()
#print(alert.get_alert_manager_alert())
