# 虚拟机XML配置管理API文档

## 概述

虚拟机XML配置管理API提供了对虚拟机XML配置的完整CRUD操作，支持版本管理、活跃版本控制，以及将配置应用到底层libvirt。

## 数据模型

### DomainXml表结构
- `id`: 主键 (UUID)
- `domain_id`: 虚拟机ID (外键)
- `xml_content`: XML配置内容 (Text)
- `xml_type`: XML类型 (String, 默认'domain')
- `version`: 版本号 (Integer)
- `is_active`: 是否为活跃版本 (Boolean)
- `description`: 配置描述 (String)
- `created_at`: 创建时间 (DateTime)
- `updated_at`: 更新时间 (DateTime)
- `created_by`: 创建者 (String)

## API接口

### 1. 创建XML配置

**POST** `/v5/vm/{domain_id}/xml/create`

**请求参数:**
```json
{
    "xml_content": "<domain>...</domain>",
    "xml_type": "domain",
    "description": "配置描述",
    "is_active": true
}
```

**响应:**
```json
{
    "msg": "XML配置创建成功",
    "code": 200,
    "data": {
        "id": "uuid",
        "version": 1
    }
}
```

### 2. 获取XML配置列表

**GET** `/v5/vm/{domain_id}/xml/list?xml_type=domain&page=1&page_size=10`

**响应:**
```json
{
    "msg": "查询成功",
    "code": 200,
    "data": {
        "total": 5,
        "page": 1,
        "page_size": 10,
        "items": [
            {
                "id": "uuid",
                "version": 2,
                "xml_type": "domain",
                "is_active": true,
                "description": "最新配置",
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "created_by": "admin"
            }
        ]
    }
}
```

### 3. 获取XML配置详情

**GET** `/v5/vm/xml/{xml_id}/detail`

**响应:**
```json
{
    "msg": "查询成功",
    "code": 200,
    "data": {
        "id": "uuid",
        "domain_id": "uuid",
        "domain_name": "vm-001",
        "xml_content": "<domain>...</domain>",
        "xml_type": "domain",
        "version": 1,
        "is_active": true,
        "description": "配置描述",
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00",
        "created_by": "admin"
    }
}
```

### 4. 更新XML配置

**PUT** `/v5/vm/xml/{xml_id}/update`

**请求参数:**
```json
{
    "xml_content": "<domain>...</domain>",
    "description": "更新后的描述",
    "is_active": true
}
```

**响应:**
```json
{
    "msg": "XML配置更新成功",
    "code": 200
}
```

### 5. 删除XML配置

**DELETE** `/v5/vm/xml/{xml_id}/delete`

**响应:**
```json
{
    "msg": "XML配置删除成功",
    "code": 200
}
```

**注意:** 不能删除活跃版本的XML配置

### 6. 激活XML配置

**POST** `/v5/vm/xml/{xml_id}/activate`

**响应:**
```json
{
    "msg": "XML配置激活成功",
    "code": 200
}
```

### 7. 获取活跃XML配置

**GET** `/v5/vm/{domain_id}/xml/active?xml_type=domain`

**响应:**
```json
{
    "msg": "查询成功",
    "code": 200,
    "data": {
        "id": "uuid",
        "domain_id": "uuid",
        "domain_name": "vm-001",
        "xml_content": "<domain>...</domain>",
        "xml_type": "domain",
        "version": 2,
        "is_active": true,
        "description": "当前活跃配置",
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00",
        "created_by": "admin"
    }
}
```

### 8. 验证XML格式

**POST** `/v5/vm/xml/validate`

**请求参数:**
```json
{
    "xml_content": "<domain>...</domain>"
}
```

**响应:**
```json
{
    "msg": "XML格式正确",
    "code": 200,
    "data": {
        "is_valid": true,
        "formatted_xml": "<domain>\n  ...\n</domain>"
    }
}
```

### 9. 应用XML配置到libvirt

**POST** `/v5/vm/xml/{xml_id}/apply`

**功能说明:** 将指定的XML配置应用到底层libvirt，实际重新定义虚拟机

**响应:**
```json
{
    "msg": "XML配置应用成功",
    "code": 200,
    "data": {
        "was_running": true,
        "message": "VM test-vm XML redefined successfully"
    }
}
```

**注意事项:**
- 如果虚拟机正在运行，会先关闭虚拟机，应用配置后重新启动
- 应用成功后，该版本会自动设为活跃版本
- 此操作会实际修改libvirt中的虚拟机定义

**使用建议:**
- 要应用活跃版本的配置，先通过 `GET /v5/vm/{domain_id}/xml/active` 获取活跃版本的ID，然后调用此接口
- 或者先通过 `POST /v5/vm/xml/{xml_id}/activate` 激活需要的版本，再应用

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误或XML格式错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用说明

1. 所有需要权限的接口都需要登录认证
2. XML配置支持版本管理，每次创建新配置会自动递增版本号
3. 每个虚拟机的每种XML类型只能有一个活跃版本
4. 不能删除活跃版本的XML配置，需要先激活其他版本
5. XML内容会自动进行格式验证
6. **应用XML配置会实际修改libvirt中的虚拟机定义，请谨慎操作**
7. 应用配置时如果虚拟机正在运行，会自动重启虚拟机

## 典型使用流程

1. 创建新的XML配置版本
2. 验证XML格式是否正确
3. 激活需要的XML版本
4. 应用XML配置到libvirt使其生效
