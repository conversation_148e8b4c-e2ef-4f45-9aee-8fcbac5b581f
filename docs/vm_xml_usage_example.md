# 虚拟机XML配置管理使用示例

## 场景：修改虚拟机内存配置

假设你需要将虚拟机的内存从2GB增加到4GB，以下是完整的操作流程：

### 1. 获取当前活跃的XML配置

```bash
curl -X GET "http://localhost:8080/v5/vm/{domain_id}/xml/active?xml_type=domain" \
  -H "Cookie: username=admin"
```

### 2. 修改XML内容

从响应中获取当前的XML配置，修改内存相关的部分：

```xml
<!-- 原配置 -->
<memory unit='KiB'>2097152</memory>
<currentMemory unit='KiB'>2097152</currentMemory>

<!-- 修改为 -->
<memory unit='KiB'>4194304</memory>
<currentMemory unit='KiB'>4194304</currentMemory>
```

### 3. 验证修改后的XML格式

```bash
curl -X POST "http://localhost:8080/v5/vm/xml/validate" \
  -H "Content-Type: application/json" \
  -H "Cookie: username=admin" \
  -d '{
    "xml_content": "<domain>...</domain>"
  }'
```

### 4. 创建新的XML配置版本

```bash
curl -X POST "http://localhost:8080/v5/vm/{domain_id}/xml/create" \
  -H "Content-Type: application/json" \
  -H "Cookie: username=admin" \
  -d '{
    "xml_content": "<domain>...</domain>",
    "xml_type": "domain",
    "description": "增加内存到4GB",
    "is_active": true
  }'
```

### 5. 应用XML配置到libvirt

从创建接口的响应中获取新配置的ID，然后应用：

```bash
curl -X POST "http://localhost:8080/v5/vm/xml/{xml_id}/apply" \
  -H "Cookie: username=admin"
```

或者，如果你想应用当前活跃的版本，可以先获取活跃版本的ID：

```bash
# 获取活跃版本信息
curl -X GET "http://localhost:8080/v5/vm/{domain_id}/xml/active?xml_type=domain" \
  -H "Cookie: username=admin"

# 然后应用该版本
curl -X POST "http://localhost:8080/v5/vm/xml/{active_xml_id}/apply" \
  -H "Cookie: username=admin"
```

## 场景：回滚到之前的配置

如果新配置有问题，需要回滚到之前的版本：

### 1. 查看所有XML配置版本

```bash
curl -X GET "http://localhost:8080/v5/vm/{domain_id}/xml/list?xml_type=domain&page=1&page_size=10" \
  -H "Cookie: username=admin"
```

### 2. 激活之前的版本

```bash
curl -X POST "http://localhost:8080/v5/vm/xml/{old_xml_id}/activate" \
  -H "Cookie: username=admin"
```

### 3. 应用回滚的配置

```bash
curl -X POST "http://localhost:8080/v5/vm/xml/{old_xml_id}/apply" \
  -H "Cookie: username=admin"
```

## 注意事项

1. **备份重要配置**: 在修改重要的生产虚拟机配置前，建议先备份当前配置
2. **测试环境验证**: 在生产环境应用前，建议在测试环境先验证配置的正确性
3. **虚拟机状态**: 应用配置时如果虚拟机正在运行，会自动重启，请确保业务可以接受短暂的中断
4. **权限控制**: 确保只有授权用户才能修改虚拟机配置
5. **监控日志**: 所有操作都会记录到系统日志中，可以通过日志追踪配置变更历史

## 常见错误处理

### XML格式错误
```json
{
    "msg": "XML格式错误: mismatched tag: line 5, column 10",
    "code": 400
}
```
**解决方案**: 检查XML语法，确保标签正确闭合

### 虚拟机不存在
```json
{
    "msg": "虚拟机不存在",
    "code": 404
}
```
**解决方案**: 检查domain_id是否正确

### libvirt连接失败
```json
{
    "msg": "连接libvirt失败: Failed to connect to socket",
    "code": 500
}
```
**解决方案**: 检查libvirt服务是否运行，网络连接是否正常
