from app.celery import app
from api.libvirt.client import Client as LibvirtClient
from api.log.log import CustomLogger
from celery import Task
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
import xml.etree.ElementTree as ET


import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class VmCreateTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )   

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 


class VmCreateCallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 


@app.task(name="create_vm")
def create_vm(result, form):
    print(f"create_vm-> result: {result}")
    print(f"create_vm-> form: {form}")
    if not form:
        print("VM creation failed.")
        return {"task_status": "failed", "error": "参数为空"}

    if form.get("status") == "failed":
        print("VM creation failed.")
        print(result.get("error"))
        return {"task_status": "failed", "error": result.get("error")}

    form["interface_info"] = result[1]

    # import time
    # time.sleep(5)

    host_ip = form.get("host_ip", "")
    client = LibvirtClient()
    res = client.create_dom_vm(client, form)
    form["spice_port"] = res.get("spice_port", 0)
    form["vnc_port"] = res.get("vnc_port", 0)
    form["status"] = res.get("status", "nostate")
    form["error"] = res.get("error") if res.get("result") == "failed" else ""
    form["task_status"] = "success" if res.get("result") == "success" else "failed"
    form["vm_xml"] = res.get("vm_xml", "")

    if form["task_status"] == "success":
        print("VM creation successful.")
    else:
        print("VM creation failed.", form.get("error"))

    return form

@app.task(name="clone_vm")
def clone_vm(disk_results, form):
    print(f"clone_vm-> disk_results: {disk_results}")
    print(f"clone_vm-> disk_results type: {type(disk_results)}")
    print(f"clone_vm-> form: {form}")
    if not form:
        print("VM creation failed.")
        return False

    if form.get("status") == "failed":
        print("VM creation failed.")
        print(form.get("error"))
        return False

    # 检查磁盘克隆结果
    # disk_results 可能是 group 的结果，需要正确处理
    if disk_results:
        print(f"Processing disk_results: {disk_results}")
        # 如果是 group 的结果，disk_results 是一个列表
        if isinstance(disk_results, list):
            for i, disk_result in enumerate(disk_results):
                print(f"Processing disk_result[{i}]: {disk_result}, type: {type(disk_result)}")
                # 处理嵌套列表结构
                if isinstance(disk_result, list):
                    for j, result in enumerate(disk_result):
                        print(f"Processing nested result[{j}]: {result}, type: {type(result)}")
                        if isinstance(result, dict) and result.get("status") == "failed":
                            print(f"Disk cloning failed: {result.get('error')}")
                            form["status"] = "failed"
                            form["error"] = f"Disk cloning failed: {result.get('error')}"
                            return form
                elif isinstance(disk_result, dict):
                    if disk_result.get("status") == "failed":
                        print(f"Disk cloning failed: {disk_result.get('error')}")
                        form["status"] = "failed"
                        form["error"] = f"Disk cloning failed: {disk_result.get('error')}"
                        return form
        else:
            # 如果是单个结果
            if disk_results.get("status") == "failed":
                print(f"Disk cloning failed: {disk_results.get('error')}")
                form["status"] = "failed"
                form["error"] = f"Disk cloning failed: {disk_results.get('error')}"
                return form

    domain_id = ""
    domain_name = ""
    
    form["interface"] = form.get("interface_info")


    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = form.get("host_ip", "")
    client = LibvirtClient()
    old_vm_name = form.get("old_vm_name")
    
    old_vm_info = client.get_vm_info_by_name(client, old_vm_name)

    # 解析并放入form
    # 虚拟化类型
    form["virtualization"] = old_vm_info.get("virtualization", "kvm")
    # vcpu
    form["vcpu_unit"] = old_vm_info.get("cpu_count")
    # 内存及单位
    memory = old_vm_info.get("memory")
    if memory is not None:
        # 假设memory单位为MB或GB
        if memory >= 1024 and memory % 1024 == 0:
            form["memory_unit"] = int(memory // 1024)
            form["memory_unit_type"] = "GB"
        else:
            form["memory_unit"] = int(memory)
            form["memory_unit_type"] = "MB"
    # 图形协议
    form["display_protocol"] = old_vm_info.get("display_protocol", "spice")
    # 输入设备
    form["input_devices"] = old_vm_info.get("input_devices", {"mouse": "usb", "keyboard": "usb"})

    res = client.create_dom_vm(client, form)
    form["spice_port"] = res.get("spice_port", 0)
    form["vnc_port"] = res.get("vnc_port", 0)
    form["task_status"] = "success"
    form["status"] = res.get("status", "nostate")
    print("VM clone successful.")
    return form

# @app.task(name="clone_vm")
# def clone_vm(form):
#     """
#     虚拟机克隆
#     :param form:
#     :return:
#     """
#     vm_name = form.get("vm_name", "")
#     host = form.get("host", "")
#     # 删除虚拟机的逻辑
#     client = LibvirtClient()
#     res = client.vm_clone(client, vm_name)
#     print(f"克隆虚拟机 VM: {vm_name}")
#     form.update(res)
#     return form


@app.task(name="delete_vm")
def delete_vm(form):
    """
    删除单个
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient()
    try:
        res = client.del_vm(client, vm_name)
        if res: 
            print(f"虚拟机删除成功 VM: {vm_name}")
            form["task_status"] = "success"
        else:
            print(f"虚拟机删除失败 VM: {vm_name}")
            form["task_status"] = "failed"
    except Exception as e:
        print(f"虚拟机删除失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form




@app.task(name="open_vm")
def open_vm(form):
    """
    开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()

    try:
        res = client.start_vm(client, vm_name)
        if res: 
            print(f"虚拟机开机 VM: {vm_name}")
            form["task_status"] = "success"
            # 获取虚拟机XML并解析spice/vnc端口
            dom = client.get_vm_info_by_name(client, vm_name)
            vnc_port = dom.get("vnc_port", 0)
            spice_port = dom.get("spice_port", 0)
            
        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
    except Exception as e:
        print(f"虚拟机开机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["vnc_port"] = 0
        form["spice_port"] = 0

    return form


@app.task(name="close_vm")
def close_vm(form):
    """
    关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()

    try:
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机关机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"


    return form



@app.task(name="destroy_vm")
def destroy_vm(form):
    """
    强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机强制关机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="pause_vm")
def pause_vm(form):
    """
    暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机暂停失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="recover_vm")
def recover_vm(form):
    """
    恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机恢复失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="reboot_vm")
def reboot_vm(form):
    """
    重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
        form["task_status"] = "success"
        dom = client.get_vm_info_by_name(client, vm_name)
       
        vnc_port = dom.get("vnc_port", 0)
        spice_port = dom.get("spice_port", 0)
           
        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
    except Exception as e:
        print(f"虚拟机重启失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["vnc_port"] = 0
        form["spice_port"] = 0
    return form


@app.task(name="restart_vm")
def restart_vm(form):
    """
    强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.force_reboot_vm(client, vm_name)
        dom = client.get_vm_info_by_name(client, vm_name)
        vnc_port = dom.get("vnc_port", 0)
        spice_port = dom.get("spice_port", 0)
        
        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
        print(f"虚拟机强制重启 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机强制重启失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="migrate_vm")
def migrate_vm(form):
    """
    迁移虚拟机
    :param form: {
        "vm_name": "虚拟机名称",
        "source_host": "源主机IP",
        "target_host": "目标主机IP",
        "migration_type": "live|offline",  # 可选，默认live
        "flags": None  # 可选，libvirt迁移标志
    }
    :return:
    """
    vm_name = form.get("vm_name", "")
    source_host = form.get("source_host", "")
    target_host = form.get("target_host", "")
    migration_type = form.get("migration_type", "live")
    flags = form.get("flags", None)

    try:
        # 连接到源主机
        source_client = LibvirtClient(host=source_host)

        # 执行迁移
        result = source_client.migrate_vm(
            source_client,
            vm_name,
            target_host,
            migration_type,
            flags
        )

        form.update(result)
        form["task_status"] = result.get("status", "failed")

        if result.get("status") == "success":
            print(f"虚拟机迁移成功: {vm_name} -> {target_host}")
        else:
            print(f"虚拟机迁移失败: {vm_name} -> {target_host}, 错误: {result.get('message')}")

    except Exception as e:
        error_msg = f"迁移虚拟机时发生异常: {str(e)}"
        print(error_msg)
        form["task_status"] = "failed"
        form["status"] = "failed"
        form["message"] = error_msg

    return form


@app.task(name="check_migration_support")
def check_migration_support(form):
    """
    检查虚拟机迁移支持
    :param form: {
        "vm_name": "虚拟机名称",
        "source_host": "源主机IP",
        "target_host": "目标主机IP"
    }
    :return:
    """
    vm_name = form.get("vm_name", "")
    source_host = form.get("source_host", "")
    target_host = form.get("target_host", "")

    try:
        # 连接到源主机
        source_client = LibvirtClient(host=source_host)

        # 检查迁移支持
        result = source_client.check_migration_support(
            source_client,
            vm_name,
            target_host
        )

        form.update(result)
        form["task_status"] = "success"

        print(f"迁移支持检查完成: {vm_name} -> {target_host}, 支持: {result.get('supported')}")

    except Exception as e:
        error_msg = f"检查迁移支持时发生异常: {str(e)}"
        print(error_msg)
        form["task_status"] = "failed"
        form["supported"] = False
        form["message"] = error_msg

    return form


@app.task(name="batch_delete_vm")
def batch_delete_vm(form):
    """
    批量删除
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        # 删除虚拟机的逻辑
        client.del_vm(client, vm_name)
        print(f"删除虚拟机 VM: {vm_name}")
    return f"批量删除虚拟机成功"


@app.task(name="batch_open_vm")
def batch_open_vm(form):
    """
    批量开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.start_vm(client, vm_name)
        print(f"虚拟机开机 VM: {vm_name}")
    return f"批量开机虚拟机成功"


@app.task(name="batch_close_vm")
def batch_close_vm(form):
    """
    批量关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
    return f"批量关机虚拟机成功"


@app.task(name="batch_destroy_vm")
def batch_destroy_vm(form):
    """
    批量强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
    return f"批量强制关机虚拟机成功"


@app.task(name="batch_pause_vm")
def batch_pause_vm(form):
    """
    批量暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
    return f"批量暂停虚拟机成功"


@app.task(name="batch_recover_vm")
def batch_recover_vm(form):
    """
    批量恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
    return f"批量恢复虚拟机成功"


@app.task(name="batch_reboot_vm")
def batch_reboot_vm(form):
    """
    批量重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
    return f"批量重启虚拟机成功"


@app.task(name="batch_restart_vm")
def batch_restart_vm(form):
    """
    批量强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_reboot_vm(client, vm_name)
        print(f"虚拟机强制重启 VM: {vm_name}")
    return f"批量强制重启虚拟机成功"


@app.task(name="create_vm_snapshot")
def create_vm_snapshot(form):
    """
    创建虚拟机快照
    :param form:
    :return:
    """
    print("create_vm_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    try:
        r = client.create_snapshot(client, form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "创建快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "创建快照返回值无效"
        print(f"创建快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")
    except Exception as e:
        print(f"创建快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form

@app.task(name="delete_vm_snapshot")
def delete_vm_snapshot(form):
    """
    删除虚拟机快照
    :param form:
    :return:
    """
    print("delete_vm_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    try:
        r = client.delete_snapshot(client, form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "删除快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "删除快照返回值无效"
        print(f"删除快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")
    except Exception as e:
        print(f"删除快照异常: {str(e)}")
        form["task_status"] = "failed"
        form["error"] = str(e)
    return form


@app.task(name="restore_vm_snapshot")
def restore_vm_snapshot(form):
    """
    虚拟机快照还原
    :param form:
    :return:
    """
    print("restore_vm_snapshot 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    try:
        r = client.restore_snapshot(client, form)
        if r and isinstance(r, dict):
            form.update(r)
            form["task_status"] = "success" if r.get("result") == "success" else "failed"
            if form["task_status"] == "failed":
                form["error"] = r.get("error", "还原快照失败")
        else:
            form["task_status"] = "failed"
            form["error"] = "还原快照返回值无效"
        print(f"还原快照{'成功' if form['task_status'] == 'success' else '失败'}: {vm_name}")
    except Exception as e:
        print(f"还原快照异常: {str(e)}")
        form["task_status"] = "failed" 
        form["error"] = str(e)
    return form


@app.task(name="snapshot_list")
def snapshot_list(form):
    """
    虚拟机快照列表
    :param form:
    :return:
    """
    print("snapshot_list 参数:", form)
    vm_name = form.get("vm_name")
    client = LibvirtClient()
    r = client.snapshot_list(client, form)
    return r