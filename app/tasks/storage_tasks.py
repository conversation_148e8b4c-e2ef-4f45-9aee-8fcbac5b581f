from nameko.standalone.rpc import ClusterRpcProxy

import settings
from app.celery import app
from celery import Task
from api.libvirt.client import Client
from utils.db import get_dbi
from db.model.hci.storage import StorageVolume, StoragePool, HostStoragePoolMapping, StorageDevice, StoragePoolGroupMapping
from config.settings import MSG_AMQP_URI
from api.iscsi.iscsi_client import ISCSIManager
from utils.tools import get_system_info

from db.model.hci.user_resource_quota import UserQuota

import logging

from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)

class StorageCreateTask(Task):
    """
    自定义存储卷钩子类
    """

    def on_success(self, retval, task_id, args, kwargs):
        storage_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"存储名称: {storage_name}")
        print(f"Form: {form}")

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        storage_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"存储名称: {storage_name}")
        print(f"Form: {form}")


class StorageCreateCallbackTask(Task):
    """
    自定义回调构造函数
    """

    def on_success(self, retval, task_id, args, kwargs):
        print("回调成功函数")
        print(args)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print("回调失败函数")
        print(args)


@app.task(name="allocate_storage")
def allocate_storage(name, form):
    print("创建存储卷")

    libvirtClient = Client(host=form["host"])
    r = libvirtClient.create_storage_pool_volume(libvirtClient, form)

    if r:
        # 获取创建的卷的详情
        volume = libvirtClient.get_storage_pool_volume_info(libvirtClient, form["storage_pool_name"], form['name']+ "." + form['volume_format'])
        print("将存储卷信息转换成数据库信息", volume)
        data = {
            "id": form["id"],
            # "name": form["name"],
            # "storage_pool_id": form["storage_pool_id"],
            # "type": form["volume_format"],
            # "join_type": form["join_type"],
            # "path": form["path"],
            # "encrypt": form["encrypt"],
            "status": volume["type"],
            # "capacity": form["capacity"],
            "allocation": volume["allocation"],
            # "preallocation": form["preallocation"],
            # "remark": form["remark"]
        }
        return data
    else:
        print("存储卷：", form["name"], " 创建失败")
    # return {"status": "success", "data": form}
    return form


@app.task(name="allocate_storage_call_back")
def allocate_storage_call_back(result):
    print("创建存储的回调函数-------")
    print("参数：", result)
    print("将创建成功的卷信息更新到数据库")

    # 获取日志相关参数
    username = result.get("username", "")
    role = result.get("role", "")
    volume_name = result.get("name", "")
    user_quota_id = result.get("user_quota_id", "")

    dbi = get_dbi()
    try:
        with dbi.session_scope() as session:
            volume = session.query(StorageVolume).filter_by(id=result["id"]).first()
            print(volume.id)
            if volume:
                # volume.status = result["status"]
                # volume.allocation = result["allocation"]
                volume = session.query(StorageVolume).filter_by(id=result["id"]).update(
                    {StorageVolume.status: result["status"], StorageVolume.allocation: result["allocation"]})


                storage_pool_id = result.get("storage_pool_id","")
                pool_cap_info = result.get("pool_cap_info")
                if storage_pool_id and pool_cap_info:
                    pool = session.query(StoragePool).filter_by(id=storage_pool_id).update(
                        {
                            StoragePool.allocation: pool_cap_info["allocation"],
                            StoragePool.capacity: pool_cap_info["capacity"],
                            StoragePool.available: pool_cap_info["available"]

                        }
                    )
                # 更新用户存储配额
                if user_quota_id:
                    user_quota = session.query(UserQuota).filter_by(id=user_quota_id).first()
                    if user_quota:
                        user_quota.storage_used = (user_quota.storage_used or 0) + (volume.capcity or 0)
                        session.commit()
                    else:
                        print(f"未找到用户配额记录: {user_quota_id}")


                session.commit()

                # 记录成功日志
                new_logger.log(
                    username, "存储", "创建存储卷", "成功", role,
                    "创建存储卷: {},成功".format(volume_name)
                )
            else:
                # 记录失败日志
                new_logger.log(
                    username, "存储", "创建存储卷", "失败", role,
                    "创建存储卷: {},失败,未找到存储卷记录".format(volume_name)
                )
    except Exception as e:
        # 记录失败日志
        new_logger.log(
            username, "存储", "创建存储卷", "失败", role,
            "创建存储卷: {},失败: {}".format(volume_name, str(e))
        )
        # TODO 用代理方式
        # nameko_config = {
        #     'AMQP_URI': MSG_AMQP_URI
        # }
        # print("广播：", MSG_AMQP_URI)
        # print("------------------")
        # body_data = {"id": str(volume.id), "name": volume.name}
        # print(body_data)
        # MESSAGE_EVENT = "refresh"
        # event = MESSAGE_EVENT
        # channel = "volume"
        # with ClusterRpcProxy(nameko_config) as cluster_rpc:
        #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
        #     websocket_hub.broadcast(channel, event, body_data)
        # print("广播结束")


@app.task(name="update_storage")
def update_storage(storage_name, form):
    print("修改卷函数")
    name = form["name"]
    new_name = form["new_name"]
    pool_name = form["pool_name"]
    import time
    libvirtClient = Client(host=form["host"])
    r = libvirtClient.rename_storage_pool_volume(libvirtClient, pool_name, name, new_name)
    return [form]


@app.task(name="update_storage_call_back")
def update_storage_call_back(result):
    # 获取日志相关参数
    username = result[0].get("username", "")
    role = result[0].get("role", "")
    old_name = result[0].get("name", "")
    new_name = result[0].get("new_name", "")

    dbi = get_dbi()

    try:
        with (dbi.session_scope() as session):
            volume = session.query(StorageVolume).filter_by(id=(result[0]["id"])).first()
            if volume:
                session.query(StorageVolume).filter_by(id=result[0]["id"]).update({StorageVolume.name : result[0]["new_name"]})
                session.commit()

                # 记录成功日志
                new_logger.log(
                    username, "存储", "修改存储卷", "成功", role,
                    "修改存储卷: {} -> {},成功".format(old_name, new_name)
                )
            else:
                # 记录失败日志
                new_logger.log(
                    username, "存储", "修改存储卷", "失败", role,
                    "修改存储卷: {} -> {},失败,未找到存储卷记录".format(old_name, new_name)
                )
        print(f"Storage update callback successfully.")
    except Exception as e:
        # 记录失败日志
        new_logger.log(
            username, "存储", "修改存储卷", "失败", role,
            "修改存储卷: {} -> {},失败: {}".format(old_name, new_name, str(e))
        )


@app.task(name="delete_storage")
def delete_storage(storage, form):
    # 删除存储的逻辑
    volume_name = form["volume_name"]
    pool_name = form["pool_name"]
    print(form)
    print(f"删除存储卷: {storage}")
    libvirtClient = Client(host=form["host"])
    r = libvirtClient.delete_storage_pool_volume(libvirtClient, pool_name, volume_name)
    return form


@app.task(name="delete_storage_call_back")
def delete_storage_call_back(result):
    # 获取日志相关参数
    username = result.get("username", "")
    role = result.get("role", "")
    volume_name = result.get("volume_name", "")
    user_quota_id = result.get("user_quota_id", "")

    # 删除存储的回调函数
    print(f"删除卷的数据库信息: {result}")
    if isinstance(result, dict) and "code" in result:
        print(f"删除存储卷失败: {result['msg']}")
        # 记录失败日志
        new_logger.log(
            username, "存储", "删除存储卷", "失败", role,
            "删除存储卷: {},失败: {}".format(volume_name, result.get('msg', ''))
        )
        return result

    dbi = get_dbi()
    try:
        with dbi.session_scope() as session:
            volume = session.query(StorageVolume).filter_by(id=result["_id"]).with_for_update().one_or_none()
            if volume:
                volume_name = volume.name  # 获取实际的卷名

                # 修改用户存储配额
                print("修改用户存储配额")
                if user_quota_id:
                    user_quota = session.query(UserQuota).filter_by(id=user_quota_id).first()
                    if user_quota:
                        user_quota.storage_used = (user_quota.storage_used or 0) - (volume.capcity or 0)
                        session.commit()
                    else:
                        print(f"未找到用户配额记录: {user_quota_id}")


                session.delete(volume)
                print("删除成功")

                storage_pool_id = result.get("storage_pool_id","")
                pool_cap_info = result.get("pool_cap_info")
                if storage_pool_id and pool_cap_info:
                    pool = session.query(StoragePool).filter_by(id=storage_pool_id).update(
                        {
                            StoragePool.allocation: pool_cap_info["allocation"],
                            StoragePool.capacity: pool_cap_info["capacity"],
                            StoragePool.available: pool_cap_info["available"]

                        }
                    )

                session.commit()

                # 记录成功日志
                new_logger.log(
                    username, "存储", "删除存储卷", "成功", role,
                    "删除存储卷: {},成功".format(volume_name)
                )
            else:
                # 记录失败日志
                new_logger.log(
                    username, "存储", "删除存储卷", "失败", role,
                    "删除存储卷: {},失败,未找到存储卷记录".format(volume_name)
                )
        return {"code": 200, "msg": "删除成功"}
    except Exception as e:
        # 记录失败日志
        new_logger.log(
            username, "存储", "删除存储卷", "失败", role,
            "删除存储卷: {},失败: {}".format(volume_name, str(e))
        )
        return {"code": 500, "msg": f"删除失败: {str(e)}"}


@app.task(name="discover_iscsi_call_back")
def discover_iscsi_call_back(form):
    """
    发现指定iscsi
    """
    host = form.get("host")
    port = form.get("port", 3260)
    iscsi = ISCSIManager(host)
    targets = iscsi.discover_targets()

    return targets


@app.task(name="iscsi_create_pool_callback")
def iscsi_create_pool_callback(form):
    print("iscsi分布式存储池回调函数：", form)
    role = form.get("role", "")
    username = form.get("username", "")
    pool_id = form.get("storage_pool_id")
    storage_local_dir = form.get("storage_local_dir")
    storage_size_str = form.get("device_size", "")  # 保持字符串形式，因为可能包含"Unknown"
    storage_result = form.get("storage_result")
    if not storage_result:
        new_logger.log(
            username, "存储", "创建存储池", "失败", role,
            "创建存储池: {},失败".format(pool_id)
        )
        print("存储池目录创建失败，不做数据库更新处理")
        return
    
    # 转换 storage_size 为整数（如果可能）
    size = 0
    if storage_size_str != "":
        try:
            size = int(storage_size_str)
        except ValueError:
            # 如果转换失败，可能是"Unknown"或其他非数字值，后续处理
            size = None  # 标记为无效值

    status = 2
    
    dbi = get_dbi()
    with dbi.session_scope() as session:
        pool = session.query(StoragePool).filter_by(id=pool_id).first()
        if not pool:
            new_logger.log(
                username, "存储", "创建存储池", "失败", role,
                "创建存储池: {},失败".format(pool_id)
            )
            print(f"未找到存储池，ID: {pool_id}")
            return False  # 或抛出异常

        if pool.status == 1 and (pool.storage_local_dir == "" or pool.storage_local_dir is None):
            # 准备更新数据
            update_data = {
                StoragePool.status: status,
                StoragePool.storage_local_dir: storage_local_dir,
            }

            # 只有 size 是有效整数时才更新容量相关字段
            if size is not None:
                update_data.update({
                    StoragePool.capacity: size,
                    StoragePool.allocation: size,
                    StoragePool.available: size
                })
            else:
                # 如果 size 无效，可以选择不更新这些字段，或者设置为默认值
                # 这里选择不更新，保留原值
                pass

            # 执行更新
            pool_update = session.query(StoragePool).filter_by(id=pool_id).update(update_data)
            session.commit()
            print(f"成功更新存储池 {pool_id}，影响行数: {pool_update}")
            new_logger.log(
                username, "存储", "创建存储池", "成功", role,
                "创建存储池: {},成功".format(pool_id)
            )
            return True
        else:
            new_logger.log(
                username, "存储", "创建存储池", "失败", role,
                "创建存储池: {},失败".format(pool_id)
            )
            print(f"存储池 {pool_id} 不满足更新条件（状态: {pool.status}，storage_local_dir: {pool.storage_local_dir}）")
            return False  # 或抛出异常
        
        
        
@app.task(name="iscsi_del_pool_callback")
def iscsi_del_pool_callback(form):
    """删除存储池的回调函数，处理数据库删除操作"""
   
    print("删除iscsi存储池回调函数") 
    print("参数：", form)
    storage_pool_id = form.get("storage_pool_id")
    del_pool_result = form.get("del_pool_result")
    
    if not del_pool_result:
        print("删除iscsi存储池目录失败，保留数据库记录")
        return form
        
    print("删除iscsi存储池目录成功，开始删除数据库记录")
    dbi = get_dbi()
        
    with dbi.session_scope() as session:
        # 删除关联表数据
        session.query(HostStoragePoolMapping).filter(
            HostStoragePoolMapping.storage_pool_id == storage_pool_id
        ).delete()
        
        # 删除存储池组与存储池的关联关系
        session.query(StoragePoolGroupMapping).filter(
            StoragePoolGroupMapping.pool_id == storage_pool_id
        ).delete()
        
        # 删除存储池
        session.query(StoragePool).filter(
            StoragePool.id == storage_pool_id
        ).delete()
        
        session.commit()
        print(f"存储池 {form.get('pool_name')} 数据库记录已删除")
    
    return form



@app.task(name="create_local_pool_callback")
def create_local_pool_call_back(form):
    print("创建本地存储池回调函数")
    print("参数：", form)

    # 获取日志相关参数
    username = form.get("username", "")
    role = form.get("role", "")
    device_id = form.get("device_id")
    host_ip = form.get("host_ip")
    pool_name = form.get("pool_name")
    storage_local_dir = form.get("storage_local_dir")
    type_info = form.get("type_info", {})
    pool_id = form.get("pool_id")
    pool_info = form.get("pool", {})
    storage_device_id = form.get("storage_device_id")
    system_info = form.get("system_info", {
        "model": "Unknown",
        "vendor": "Unknown"
    })

    dbi = get_dbi()

    try:
        with dbi.session_scope() as session:

            # 存储设备信息处理
            if storage_device_id:
                storage_device = session.query(StorageDevice).filter_by(id=storage_device_id).first()
                if storage_device.model != system_info["model"] or storage_device.vendor != system_info["vendor"]:
                    storage_device.model = system_info["model"]
                    storage_device.vendor = system_info["vendor"]
                    session.commit()
                    print(f"更新存储设备信息：{storage_device_id}，name： {storage_device.device_name}，型号：{system_info['model']}，厂商：{system_info['vendor']}")

            pool = session.query(StoragePool).filter_by(id=pool_id).first()
            if not pool:
                print(f"未找到存储池，ID: {pool_id}")
                # 记录失败日志
                new_logger.log(
                    username, "存储", "创建本地存储池", "失败", role,
                    "创建本地存储池: {},失败,未找到存储池记录".format(pool_name)
                )
                return form

            # 更新存储池信息
            # pool.storage_local_dir = storage_local_dir
            pool.type_code = type_info.get("code")
            pool.type_code_display = type_info.get("name")
            pool.storage_device_id = device_id
            pool.storage_local_dir = pool_info.get("storage_local_dir")
            pool.status = pool_info.get("status")
            pool.capacity = pool_info.get("capacity")
            pool.allocation = pool_info.get("allocation")
            pool.available = pool_info.get("available")

            session.commit()

            # 记录成功日志
            new_logger.log(
                username, "存储", "创建本地存储池", "成功", role,
                "创建本地存储池: {},成功".format(pool_name)
            )

        print("创建本地存储池成功，返回的参数信息：", form)
        return form
    except Exception as e:
        # 记录失败日志
        new_logger.log(
            username, "存储", "创建本地存储池", "失败", role,
            "创建本地存储池: {},失败: {}".format(pool_name, str(e))
        )
        return form


@app.task(name="put_local_pool_callback")
def put_local_pool_callback(form):
    print("修改本地存储池回调函数")
    print("参数：", form)

    # 获取日志相关参数
    username = form.get("username", "")
    role = form.get("role", "")
    pool_id = form.get("id")
    name = form.get("name")
    new_name = form.get("new_name")

    dbi = get_dbi()
    try:
        with dbi.session_scope() as session:
            # 更新存储池名称
            # pool = session.query(StoragePool).filter_by(id=pool_id).update(
            #     {StoragePool.name: new_name}
            # )
            # session.commit()
            pool = session.query(StoragePool).filter_by(id=pool_id).first()
            if pool and pool.name == new_name:
                print(f"存储池 {name} 名称已更新为 {new_name}")
                # 记录成功日志
                new_logger.log(
                    username, "存储", "修改本地存储池", "成功", role,
                    "修改本地存储池: {} -> {},成功".format(name, new_name)
                )
            else:
                print(f"底层存储池 {name} 名称更新失败")
                # 记录失败日志
                new_logger.log(
                    username, "存储", "修改本地存储池", "失败", role,
                    "修改本地存储池: {} -> {},失败,底层存储池名称更新失败".format(name, new_name)
                )

        return form
    except Exception as e:
        # 记录失败日志
        new_logger.log(
            username, "存储", "修改本地存储池", "失败", role,
            "修改本地存储池: {} -> {},失败: {}".format(name, new_name, str(e))
        )
        return form
    
    
@app.task(name="delete_local_pool_callback")
def delete_local_pool_callback(form):
    print("删除本地存储池回调函数")
    print("参数：", form)

    # 获取日志相关参数
    username = form.get("username", "")
    role = form.get("role", "")
    pool_id = form.get("id")
    pool_name = form.get("pool_name")

    dbi = get_dbi()
    with dbi.session_scope() as session:
        try:
            # 删除存储池与主机的关联关系
            session.query(HostStoragePoolMapping).filter(
                HostStoragePoolMapping.storage_pool_id == pool_id
            ).delete()

            # 删除存储池
            session.query(StoragePool).filter(
                StoragePool.id == pool_id
            ).delete()

            session.commit()
            print(f"存储池 {pool_name} 及其关联信息已从数据库中删除")

            # 记录成功日志
            new_logger.log(
                username, "存储", "删除本地存储池", "成功", role,
                "删除本地存储池: {},成功".format(pool_name)
            )
            return {"code": 200, "msg": "删除成功"}

        except Exception as e:
            print(f"删除存储池 {pool_name} 数据库记录时发生错误: {str(e)}")
            session.rollback()

            # 记录失败日志
            new_logger.log(
                username, "存储", "删除本地存储池", "失败", role,
                "删除本地存储池: {},失败: {}".format(pool_name, str(e))
            )
            return {"code": 500, "msg": f"删除数据库记录失败: {str(e)}"}


@app.task(name="create_ceph_pool_callback")
def create_ceph_pool_callback(form):
    """
    创建 Ceph 存储池的回调函数
    处理在主机上创建 libvirt secret 的结果
    """
    print("创建ceph存储池回调函数")
    print("参数：", form)

    # 获取日志相关参数
    username = form.get("username", "")
    role = form.get("role", "")
    host_ip = form.get("host_ip", "")
    pool_name = form.get("pool_name", "")
    success = form.get("success", False)

    try:
        if success:
            secret_uuid = form.get("secret_uuid", "")
            message = form.get("message", "")

            # 记录成功日志
            new_logger.log(
                username, "存储", "创建Ceph存储池Secret", "成功", role,
                f"在主机 {host_ip} 上为存储池 {pool_name} 创建 libvirt secret 成功: {secret_uuid}"
            )

            print(f"✅ 主机 {host_ip}: {message}")

        else:
            error = form.get("error", "未知错误")

            # 记录失败日志
            new_logger.log(
                username, "存储", "创建Ceph存储池Secret", "失败", role,
                f"在主机 {host_ip} 上为存储池 {pool_name} 创建 libvirt secret 失败: {error}"
            )

            print(f"❌ 主机 {host_ip}: {error}")

    except Exception as e:
        error_msg = f"处理创建 Ceph 存储池回调异常: {str(e)}"
        print(error_msg)

        # 记录异常日志
        new_logger.log(
            username, "存储", "创建Ceph存储池Secret", "失败", role,
            f"处理主机 {host_ip} 创建 libvirt secret 回调异常: {str(e)}"
        )

    return form


@app.task(name="delete_ceph_pool_callback")
def delete_ceph_pool_callback(form):
    """
    删除 Ceph 存储池的回调函数
    处理在主机上删除 libvirt secret 的结果
    """
    print("删除ceph存储池回调函数")
    print("参数：", form)

    # 获取日志相关参数
    username = form.get("username", "")
    role = form.get("role", "")
    host_ip = form.get("host_ip", "")
    pool_name = form.get("pool_name", "")
    success = form.get("success", False)

    try:
        if success:
            message = form.get("message", "")

            # 记录成功日志
            new_logger.log(
                username, "存储", "删除Ceph存储池Secret", "成功", role,
                f"在主机 {host_ip} 上为存储池 {pool_name} 删除 libvirt secret 成功"
            )

            print(f"✅ 主机 {host_ip}: {message}")

        else:
            error = form.get("error", "未知错误")

            # 记录失败日志
            new_logger.log(
                username, "存储", "删除Ceph存储池Secret", "失败", role,
                f"在主机 {host_ip} 上为存储池 {pool_name} 删除 libvirt secret 失败: {error}"
            )

            print(f"❌ 主机 {host_ip}: {error}")

    except Exception as e:
        error_msg = f"处理删除 Ceph 存储池回调异常: {str(e)}"
        print(error_msg)

        # 记录异常日志
        new_logger.log(
            username, "存储", "删除Ceph存储池Secret", "失败", role,
            f"处理主机 {host_ip} 删除 libvirt secret 回调异常: {str(e)}"
        )

    return form



