from app.celery import app
from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient

from utils.db import get_dbi
from db.model.hci.network import Switch, SwitchPorts, SwitchPortGroups
from db.model.hci.compute import Domain, DomainDisk, DomainInterface, Host
from db.model.hci.storage import StoragePool, StorageVolume
import traceback
from celery import Task, group
import requests
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
from config.settings import QUEUE_NAME
from app.agents.vm_tasks import (
    open_vm, close_vm, destroy_vm, pause_vm, 
    recover_vm, reboot_vm, restart_vm,
    delete_vm,
)
from db.model.hci.user_resource_quota import (
    UserQuota, UserVmAssignment, UserHostAssignment, UserClusterAssignment, UserStoragePool
)
from api.log.log import CustomLogger
import logging

logger = logging.getLogger(__name__)
from config import settings
from api.log.log import CustomLogger
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)


class VmCreateTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )


class VmCreateCallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )


@app.task(name="create_vm_callback_abandon")
def create_vm_callback_abandon(results):
    # 废弃
    print(f"Task completed! The result is: {results}")

    # dbi = get_dbi()

    # # with dbi.session_scope() as session:
    # #     domain_list = session.query(Domain).all()
    # #     print(domain_list)

    # 通过results 来合并虚机的json信息
    form = {}
    interface = []
    disk = []
    domain_id = ""
    domain_name = ""

    # 测试等10秒
    import time
    time.sleep(20)

    for result in results:
        if result.get("status") == "failed":
            print("VM creation failed.")
            print(result.get("error"))
            return False

        if result.get("form", {}):
            form = result.get("form", {})
            if form:
                domain_id = form.get("domain_id", "")
                domain_name = form.get("name", "")

        if result.get("interface", []):
            interface = result.get("interface", [])

        if result.get("disk", []):
            disk = result.get("disk", [])

    data = form.copy()
    data["interface"] = interface
    data["disk"] = disk

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = data.get("host_ip", "")
    # client = LibvirtClient(host_ip)
    # res = client.create_dom_vm(client, data)

    print("VM creation successful.")

    dbi = get_dbi()
    with dbi.session_scope() as session:

        # 通过domain_id来修改domain表的状态
        domain = session.query(Domain).filter(Domain.id == domain_id).first()
        domain.status = "running"
        session.add(domain)

        # 根据disk_id 需要修改盘和虚机的关系
        disks = data.get("disk", [])
        for disk in disks:
            disk_id = disk.get("disk_id", "")
            pass

        # 根据port_id 需要修改网卡和虚机的关系
        interfaces = data.get("interface", [])
        for interface in interfaces:
            port_id = interface.get("port_id", "")
            pass

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdroms = data.get("cdrom", [])
        for cdrom in cdroms:
            cdrom_id = cdrom.get("cdrom_id", "")
            pass

    # TODO 用代理方式
    nameko_config = {
        'AMQP_URI': settings.MSG_AMQP_URI
    }
    body_data = {"id": domain_id, "name": domain_name}
    MESSAGE_EVENT = "refresh"
    event = MESSAGE_EVENT
    channel = "vm_list"
    with ClusterRpcProxy(nameko_config) as cluster_rpc:
        websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
        websocket_hub.broadcast(channel, event, body_data)

    # TODO 发送消息总线上 用rest api方式
    # channel_vm_list_path = "http://10.168.2.4:9000/send/vm_list"
    # data = {"id": "vm_table", "action": "refresh"}

    # #requests 发送 rest
    # # Send the POST request
    # response = requests.post(channel_vm_list_path, json=data)
    # # Check the response
    # if response.status_code == 200:
    #     print("Request was successful")
    #     print("Response data:", response.json())
    # else:
    #     print(f"Request failed with status code: {response.status_code}")
    #     print("Response content:", response.text)


@app.task(base=VmCreateCallbackTask, name="hello")
def hello(results):
    print("hello")


@app.task(base=VmCreateCallbackTask, name="create_vm_callback_new")
def create_vm_callback_new(info, result):
    print(f"Task completed! The result is: {result}")
    if not result:
        print("VM creation failed.")
        return False

    if result.get("status") == "failed":
        print("VM creation failed.")
        print(result.get("error"))
        return False

    domain_id = ""
    domain_name = ""

    # 测试等10秒
    import time
    time.sleep(5)

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = result.get("host_ip", "")
    client = LibvirtClient(host_ip)
    res = client.create_dom_vm(client, result)

    print("VM creation successful.")

    dbi = get_dbi()
    with dbi.session_scope() as session:

        if result.get("domain_id"):
            domain_id = result.get("domain_id", "")
            session.query(Domain).filter(Domain.id == domain_id).update(
                {Domain.status: "running"})
            session.commit()

        # 根据disk_id 需要修改盘和虚机的关系
        if result.get("disk", []):
            disks = result.get("disk", [])
            for disk in disks:
                disk_id = disk.get("disk_id", "")
                pass

        # 根据port_id 需要修改网卡和虚机的关系
        if result.get("interface", []):
            interfaces = result.get("interface", [])
            for interface in interfaces:
                port_id = interface.get("port_id", "")
                pass

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdroms = result.get("cdrom", [])
        for cdrom in cdroms:
            cdrom_id = cdrom.get("cdrom_id", "")
            pass

    # #TODO 用代理方式
    # nameko_config = {
    #     'AMQP_URI': settings.MSG_AMQP_URI
    # }
    # body_data = {"id": domain_id, "name": domain_name }
    # MESSAGE_EVENT = "refresh"
    # event = MESSAGE_EVENT
    # channel = "vm_list"
    # with ClusterRpcProxy(nameko_config) as cluster_rpc:
    #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
    #     websocket_hub.broadcast(channel, event, body_data)


@app.task(name="create_vm_callback")
def create_vm_callback(result):

    role = result.get("role", "")
    username = result.get("username", "")
    vm_name = result.get("vm_name", "")
    vm_xml = result.get("vm_xml", "")

    print(f"create_vm_callback:{result}")
    domain_id = result.get("domain_id")
    host_id = result.get("host_id")
    host_ip = result.get("host_ip", "")
    user_quota_id = result.get("user_quota_id", "")

    task_result = result.get("task_status", False)

    if task_result:
        dbi = get_dbi()
        with dbi.session_scope() as session:
            spice_port = result.get("spice_port", 0)
            vnc_port = result.get("vnc_port", 0)
            status = result.get("status", "error")

            if result.get("domain_id"):
                domain_id = result.get("domain_id", "")
                domain = session.query(Domain).filter(
                    Domain.id == domain_id).first()
                if domain:
                    domain.status = status
                    domain.spice_port = spice_port
                    domain.vnc_port = vnc_port
                    session.commit()
                else:
                    print(f"未找到虚拟机: {domain_id}")

            # 更新配额
            if user_quota_id and domain:
                user_quota = session.query(UserQuota).filter_by(
                    id=user_quota_id).first()
                if user_quota:
                    user_quota.cpu_used = (
                        user_quota.cpu_used or 0) + (domain.vcpu or 0)
                    user_quota.memory_used = (
                        user_quota.memory_used or 0) + (domain.memory or 0)
                    session.commit()
                else:
                    print(f"未找到用户配额记录: {user_quota_id}")

            # 根据disk_id 需要修改盘和虚机的关系
            disks = result.get("disk", [])
            domain_disks = []
            for disk in disks:
                disk_id = disk.get("disk_id", "")
                pool_id = disk.get("pool_id", "")
                domain_disk = DomainDisk(
                    domain_id=domain_id,
                    storage_pool_id=pool_id,
                    storage_vol_id=disk_id,
                    type_code="file",  # 默认文件类型
                    device="disk",     # 默认磁盘设备
                    dev="vda",         # 默认设备名（可根据实际情况调整）
                    bus="virtio",      # 默认总线类型
                    qemu_type="qcow2",  # 默认磁盘格式
                    boot_order=0,      # 默认引导顺序
                    host_id=host_id,  # 必须提供 host_id（外键）
                )
                domain_disks.append(domain_disk)
            if domain_disks:
                session.add_all(domain_disks)

            # 根据port_id 需要修改网卡和虚机的关系
            interfaces = result.get("interface", [])
            domain_interfaces = []
            for interface in interfaces:
                port_id = interface.get("switch_port_id", "")
                ip = interface.get("ip", "")
                mac = interface.get("mac", "")
                domain_interface = DomainInterface(
                    domain_id=domain_id,
                    switch_port_id=port_id,
                    type_code="bridge",  # 默认桥接模式
                    mac=mac,  # 生成随机或默认 MAC
                    model="virtio",     # 默认网卡型号
                    ip=ip,
                )
                domain_interfaces.append(domain_interface)
            if domain_interfaces:
                session.add_all(domain_interfaces)

            # 根据cdrom_id 需要修改光驱和虚机的关系
            cdrom_disks = []
            cdroms = result.get("cdrom", [])
            for cdrom in cdroms:
                disk_id = cdrom.get("disk_id", "")
                if not disk_id:
                    continue
                pool_id = cdrom.get("pool_id", "")
                cdrom_disk = DomainDisk(
                    domain_id=domain_id,
                    storage_pool_id=pool_id,
                    storage_vol_id=disk_id,
                    type_code="file",   # 光驱通常是文件
                    device="cdrom",     # 设备类型为光驱
                    dev="hda",          # 光驱通常用 ide 总线
                    bus="ide",          # 光驱总线类型
                    qemu_type="raw",    # 光驱格式通常为 raw
                    boot_order=1,       # 光驱引导顺序
                    host_id=host_id,  # 必须提供 host_id
                )
                cdrom_disks.append(cdrom_disk)
            if cdrom_disks:
                session.add_all(cdrom_disks)

            if vm_xml != "":
                xml = DomainXml(
                    domain_id=domain_id,
                    xml_content=vm_xml,
                    xml_type="domain",
                    version=1,
                    is_active=True,
                    description="虚拟机创建时的初始XML配置",
                    created_by=username
                )
                session.add(xml)
                session.commit()

        new_logger.log(
            username, "虚拟机", "创建", "成功", role, "{} 创建云主机: {},成功".format(
                role, vm_name)
        )
    else:

        new_logger.log(
            username, "虚拟机", "创建", "失败", role, "{} 创建云主机: {},失败".format(
                role, vm_name)
        )

    # #TODO 用代理方式
    # nameko_config = {
    #     'AMQP_URI': settings.MSG_AMQP_URI
    # }
    # body_data = {"id": domain_id, "name": domain_name }
    # MESSAGE_EVENT = "refresh"
    # event = MESSAGE_EVENT
    # channel = "vm_list"
    # with ClusterRpcProxy(nameko_config) as cluster_rpc:
    #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
    #     websocket_hub.broadcast(channel, event, body_data)


@app.task(name="clone_vm_callback")
def clone_vm_callback(result):
    print(f"clone_vm_callback:{result}")

    role = result.get("role", "")
    username = result.get("username", "")
    vm_name = result.get("vm_name", "")

    task_result = result.get("task_status", False)
    clone_tag = result.get("clone_tag", "full")

    domain_id = result.get("domain_id")
    # host_id = result.get("host_id")
    # host_ip = result.get("host_ip", "")

    dbi = get_dbi()

    if task_result:

        with dbi.session_scope() as session:
            spice_port = result.get("spice_port", "")
            vnc_port = result.get("vnc_port", "")
            status = result.get("status", "")

            if result.get("domain_id"):
                domain_id = result.get("domain_id", "")
                session.query(Domain).filter(Domain.id == domain_id).update(
                    {Domain.status: status,
                     Domain.spice_port: spice_port,
                     Domain.vnc_port: vnc_port
                     })
                session.commit()
                print("记录克隆成功日志")

                if clone_tag == "full":
                    print("记录完全克隆成功日志")
                    new_logger.log(
                        username, "虚拟机", "完全克隆", "成功", role,
                        "虚拟机完全克隆: {},成功".format(vm_name)
                    )
                if clone_tag == "link":
                    print("记录链接克隆成功日志")
                    new_logger.log(
                        username, "虚拟机", "链接克隆", "成功", role,
                        "虚拟机链接克隆: {},成功".format(vm_name)
                    )
                
    else:
        if clone_tag == "full":
            print("记录完全克隆成功日志")
            new_logger.log(
                username, "虚拟机", "完全克隆", "失败", role,
                "虚拟机完全克隆: {},失败".format(vm_name)
            )
            
        if clone_tag == "link":
            print("记录链接克隆成功日志")
            new_logger.log(
                username, "虚拟机", "链接克隆", "失败", role,
                "虚拟机链接克隆: {},失败".format(vm_name)
            )
            


@app.task(name="clone_vm")
def clone_vm(form):
    """
    虚拟机克隆
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient(host)
    client.vm_clone(client, vm_name)
    print(f"克隆虚拟机 VM: {vm_name}")
    return f"VM {vm_name} clone successfully."



@app.task(name="distach_op_vm_del")
def distach_op_vm_del(form):
    """
    删除虚拟机任务分发
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = delete_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | delete_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="delete_vm_callback")
def delete_vm_callback(form):
    """
    删除单个
    :param form:
    :return:
    """
    print(f"delete_vm_callback:{form}")
    dbi = get_dbi()

    for vm in form:
        username = vm.get("username", "")
        role = vm.get("role", "")
        vm_name = vm.get("vm_name", "")
        _id = vm.get("vm_id", "")
        task_status = vm.get("task_status", "")
        if task_status == "success":
            with dbi.session_scope() as session:
                # 判断用户和虚拟机的绑定关系
                # 1. 判断用户和虚拟机的绑定关系
                user_vm_assignment = session.query(UserVmAssignment).filter(
                    UserVmAssignment.vm_id == _id
                ).first()
                if user_vm_assignment:
                    user_id = user_vm_assignment.user_id
                    # 2. 获取用户配额信息
                    user_quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
                    # 3. 获取虚拟机资源信息
                    domain = session.query(Domain).filter(Domain.id == _id).first()
                    if user_quota and domain:
                        # 扣减配额（防止负数）
                        user_quota.cpu_used = max((user_quota.cpu_used or 0) - (domain.vcpu or 0), 0)
                        user_quota.memory_used = max((user_quota.memory_used or 0) - (domain.memory or 0), 0)
                
                
                    # 删除用户和虚拟机的绑定关系
                    session.query(UserVmAssignment).filter(UserVmAssignment.vm_id == _id).delete()

                # 删除虚拟机和磁盘的绑定关系，磁盘本身不做删除
                session.query(DomainDisk).filter(DomainDisk.domain_id == _id).delete()
                
                # 查询和虚拟机绑定的网络端口id
                switch_port_ids_info = session.query(DomainInterface.switch_port_id)\
                                    .filter(DomainInterface.domain_id == _id)\
                                    .all()  # 返回的是元组列表，如 [(1,), (2,)]
                # 提取纯 ID 列表
                switch_port_ids = [id[0] for id in switch_port_ids_info if id[0] is not None]
                
                # 批量删除 switch_port 表中的记录
                # 删除相关的网络端口的数据，
                # 网络端口只有在虚拟机运行的时候才会实际创建，所以虚拟机删除的时候将网络端口数据删除
                if switch_port_ids:
                    deleted_count = session.query(SwitchPorts)\
                                        .filter(SwitchPorts.id.in_(switch_port_ids))\
                                        .delete(synchronize_session=False)
                # 删除虚拟机和网络端口的绑定关系                      
                session.query(DomainInterface).filter(DomainInterface.domain_id == _id).delete()
                
                
                
                # 删除对应的虚拟机数据
                session.query(Domain).filter(Domain.id == _id).delete()
                session.commit()

            new_logger.log(
                username, "虚拟机", "删除虚拟机", "成功", role,
                f"删除虚拟机: {vm_name} 成功"
            )

        if task_status == "failed":
            e = vm.get("error", "")
            print(f"删除虚拟机 {vm_name} 失败: {str(e)}")
            new_logger.log(
                username, "虚拟机", "删除虚拟机", "失败", role,
                f"删除虚拟机: {vm_name} 失败, 错误: {str(e)}"
            )
    print(f"删除虚拟机任务执行完成")
    return f"VM {vm_name} deleted successfully."


@app.task(name="distach_op_vm_open")
def distach_op_vm_open(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = open_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | open_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


# @app.task(name="open_vm")
# def open_vm(form):
#     """
#     开机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)

#     client.start_vm(client, vm_name)
#     print(f"虚拟机开机 VM: {vm_name}")
#     return f"开机虚拟机成功"

@app.task(name="open_vm_callback")
def open_vm_callback(form):
    """
    开机
    :param form:
    :return:
    """
    print("open_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            print("记录完全克隆成功日志")
            new_logger.log(
                username, "虚拟机", "开机", "成功", role,
                "虚拟机开机: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"开机虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "开机", "失败", role,
                "虚拟机开机: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()

    result = f"虚拟机开机任务执行完成 开机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


@app.task(name="distach_op_vm_close")
def distach_op_vm_close(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = close_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | close_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="close_vm_callback")
def close_vm_callback(form):
    """
    关机
    :param form:
    :return:
    """
    print("close_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "关机", "成功", role,
                "虚拟机关机: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            new_logger.log(
                username, "虚拟机", "关机", "失败", role,
                "虚拟机关机: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutoff",
                 Domain.spice_port: 0,  # 清空动态端口
                 Domain.vnc_port: 0      # 清空动态端口
                 },
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机关机任务执行完成 关机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="close_vm")
# def close_vm(form):
#     """
#     关机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.stop_vm(client, vm_name)
#     print(f"虚拟机关机 VM: {vm_name}")
#     return f"关机虚拟机成功"

@app.task(name="distach_op_vm_destroy")
def distach_op_vm_destroy(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = destroy_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | destroy_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="destroy_vm_callback")
def destroy_vm_callback(form):
    """
    强制关机
    :param form:
    :return:
    """
    print("destroy_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "强制关机", "成功", role,
                "虚拟机强制关机: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"强制关机虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "强制关机", "失败", role,
                "虚拟机强制关机: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutoff",
                 Domain.spice_port: 0,  # 清空动态端口
                 Domain.vnc_port: 0      # 清空动态端口
                 },
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机强制关机任务执行完成 强制关机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="destroy_vm")
# def destroy_vm(form):
#     """
#     强制关机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.force_stop_vm(client, vm_name)
#     print(f"虚拟机强制关机 VM: {vm_name}")
#     return f"强制关机虚拟机成功"


@app.task(name="distach_op_vm_pause")
def distach_op_vm_pause(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = pause_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | pause_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="pause_vm_callback")
def pause_vm_callback(form):
    """
    暂停
    :param form:
    :return:
    """
    print("pause_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "暂停", "成功", role,
                "虚拟机暂停: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"暂停虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "暂停", "失败", role,
                "虚拟机暂停: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "paused"},
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机暂停任务执行完成 暂停成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="pause_vm")
# def pause_vm(form):
#     """
#     暂停
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.suspend_vm(client, vm_name)
#     print(f"虚拟机暂停 VM: {vm_name}")
#     return f"批量暂停虚拟机成功"


@app.task(name="distach_op_vm_recover")
def distach_op_vm_recover(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = recover_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | recover_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="recover_vm_callback")
def recover_vm_callback(form):
    """
    恢复
    :param form:
    :return:
    """
    print("recover_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []

    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "恢复", "成功", role,
                "虚拟机恢复: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"恢复虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "恢复", "失败", role,
                "虚拟机恢复: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "running"},
                synchronize_session=False
            )
            session.commit()

    result = f"虚拟机恢复任务执行完成 恢复成功 {success_count} 失败 {failed_count}"
    print(result)
    return result

# @app.task(name="recover_vm")
# def recover_vm(form):
#     """
#     恢复
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.resume_vm(client, vm_name)
#     print(f"虚拟机恢复 VM: {vm_name}")
#     return f"批量恢复虚拟机成功"


@app.task(name="distach_op_vm_reboot")
def distach_op_vm_reboot(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = reboot_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | reboot_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="reboot_vm_callback")
def reboot_vm_callback(form):
    """
    重启
    :param form:
    :return:
    """
    print("reboot_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据
    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "重启", "成功", role,
                "虚拟机重启: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"重启虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "重启", "失败", role,
                "虚拟机重启: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()

    result = f"虚拟机重启任务执行完成 重启成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="reboot_vm")
# def reboot_vm(form):
#     """
#     重启
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.reboot_vm(client, vm_name)
#     print(f"虚拟机重启 VM: {vm_name}")
#     return f"批量重启虚拟机成功"


@app.task(name="distach_op_vm_restart")
def distach_op_vm_restart(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_" + ip

        # 分发子任务
        job = restart_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | restart_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="restart_vm_callback")
def restart_vm_callback(form):
    """
    强制重启
    :param form:
    :return:
    """
    print("restart_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据
    # 先统计成功和失败的虚拟机
    for vm in form:
        role = vm.get("role", "")
        username = vm.get("username", "")
        
        vm_name = vm.get("name", "")
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
            new_logger.log(
                username, "虚拟机", "强制重启", "成功", role,
                "虚拟机强制重启: {},成功".format(vm_name)
            )
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"强制重启虚拟机失败: {vm.get('name')}")
            new_logger.log(
                username, "虚拟机", "强制重启", "失败", role,
                "虚拟机强制重启: {},失败".format(vm_name)
            )

    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()

    result = f"虚拟机强制重启任务执行完成 强制重启成功 {success_count} 失败 {failed_count}"
    print(result)
    return result

# @app.task(name="restart_vm")
# def restart_vm(form):
#     """
#     强制重启
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.force_reboot_vm(client, vm_name)
#     print(f"虚拟机强制重启 VM: {vm_name}")
#     return f"批量强制重启虚拟机成功"


@app.task(name="batch_delete_vm")
def batch_delete_vm(form):
    """
    批量删除
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        # 删除虚拟机的逻辑
        client.del_vm(client, vm_name)
        print(f"删除虚拟机 VM: {vm_name}")
    return f"批量删除虚拟机成功"


@app.task(name="batch_open_vm")
def batch_open_vm(form):
    """
    批量开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.start_vm(client, vm_name)
        print(f"虚拟机开机 VM: {vm_name}")
    return f"批量开机虚拟机成功"


@app.task(name="batch_close_vm")
def batch_close_vm(form):
    """
    批量关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
    return f"批量关机虚拟机成功"


@app.task(name="batch_destroy_vm")
def batch_destroy_vm(form):
    """
    批量强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
    return f"批量强制关机虚拟机成功"


@app.task(name="batch_pause_vm")
def batch_pause_vm(form):
    """
    批量暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
    return f"批量暂停虚拟机成功"


@app.task(name="batch_recover_vm")
def batch_recover_vm(form):
    """
    批量恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
    return f"批量恢复虚拟机成功"


@app.task(name="batch_reboot_vm")
def batch_reboot_vm(form):
    """
    批量重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
    return f"批量重启虚拟机成功"


@app.task(name="batch_restart_vm")
def batch_restart_vm(form):
    """
    批量强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_reboot_vm(client, vm_name)
        print(f"虚拟机强制重启 VM: {vm_name}")
    return f"批量强制重启虚拟机成功"


@app.task(name="create_start_vm_migration")
def create_start_vm_migration(form):
    """
    分发虚拟机迁移操作
    :param form: 迁移任务列表，每个元素包含虚拟机迁移信息
    :return:
    """

    from app.agents.vm_tasks import migrate_vm

    jobs = []
    for target in form:
        # 从每个target中获取迁移信息
        vm_name = target.get("vm_name", "")
        vm_id = target.get("vm_id", "")
        source_host = target.get("source_host", "")
        target_host = target.get("target_host", "")
        migration_type = target.get("migration_type", "live")
        username = target.get("username", "")
        role = target.get("role", "")

        # 构建迁移任务数据
        migration_form = {
            "vm_name": vm_name,
            "vm_id": vm_id,  # 添加vm_id用于回调更新数据库
            "source_host": source_host,
            "target_host": target_host,
            "migration_type": migration_type,
            "username": username,
            "role": role,
        }

        # 使用源主机的队列
        queue_name = "queue_" + source_host

        # 分发子任务到对应的主机队列
        job = migrate_vm.s(migration_form).set(queue=queue_name)
        jobs.append(job)

    if jobs:
        # 使用 group 来聚合所有子任务，并指定最终的回调函数
        job_group = group(jobs) | start_vm_migration_callback.s().set(
            queue=QUEUE_NAME)
        result = job_group.apply_async()
        print(f"已分发 {len(jobs)} 个虚拟机迁移任务")
        return f"分发了 {len(jobs)} 个迁移任务"
    else:
        print("没有需要迁移的虚拟机")
        return "没有需要迁移的虚拟机"


@app.task(name="start_vm_migration_callback")
def start_vm_migration_callback(results):
    """
    开始虚拟机迁移回调
    :param results: 迁移任务结果列表
    :return:
    """
    print("start_vm_migration_callback 参数:", results)

    from db.model.hci.compute import Domain, Host

    dbi = get_dbi()
    success_count = 0
    failed_count = 0

    # 处理每个迁移结果
    for result in results:
        if not isinstance(result, dict):
            continue

        vm_name = result.get("vm_name", "")
        vm_id = result.get("vm_id", "")
        target_host = result.get("target_host", "")
        task_status = result.get("task_status", "failed")
        username = result.get("username", "")
        role = result.get("role", "")

        try:
            if task_status == "success":
                # 更新数据库中虚拟机的主机信息
                with dbi.session_scope() as session:
                    # 查找目标主机
                    target_host_obj = session.query(Host).filter(
                        Host.ip == target_host).first()
                    if target_host_obj and vm_id:
                        # 更新虚拟机的主机ID
                        session.query(Domain).filter(Domain.id == vm_id).update({
                            Domain.host_id: target_host_obj.id
                        })
                        session.commit()
                        print(f"已更新虚拟机 {vm_name} 的主机信息到 {target_host}")

                success_count += 1
                new_logger.log(
                    username, "虚拟机", "迁移", "成功", role,
                    f"虚拟机迁移: {vm_name} 到 {target_host}, 成功"
                )
                print(f"虚拟机迁移成功: {vm_name} 到 {target_host}")
            else:
                failed_count += 1
                new_logger.log(
                    username, "虚拟机", "迁移", "失败", role,
                    f"虚拟机迁移: {vm_name} 到 {target_host}, 失败"
                ) 
                print(
                    f"虚拟机迁移失败: {vm_name}, 错误: {result.get('message', '未知错误')}")

        except Exception as e:
            new_logger.log(
                username, "虚拟机", "迁移", "失败", role,
                f"虚拟机迁移: {vm_name} 到 {target_host}, 错误: {str(e)}"
            )
            print(f"处理迁移回调时发生错误: {vm_name}, 错误: {str(e)}")
            failed_count += 1

    result_msg = f"虚拟机迁移任务完成 - 成功: {success_count}, 失败: {failed_count}"
    print(result_msg)

    return {
        "message": result_msg,
        "success_count": success_count,
        "failed_count": failed_count,
        "results": results
    }


@app.task(name="create_vm_snapshot_callback")
def create_vm_snapshot_callback(form):
    """
    创建虚拟机快照回调
    :param form:
    :return:
    """
    print("create_vm_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")

    if task_status == "success":
        new_logger.log(
            username, "虚拟机", "创建快照", "成功", role,
            f"虚拟机: {vm_name} 创建快照: {snapshot_name} 成功"
        )
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "创建快照", "失败", role,
            f"虚拟机: {vm_name} 创建快照: {snapshot_name} 失败, 错误: {error}"
        )
    return form

@app.task(name="delete_vm_snapshot_callback")
def delete_vm_snapshot_callback(form):
    """
    删除虚拟机快照回调
    :param form:
    :return:
    """
    print("delete_vm_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")

    if task_status == "success":
        new_logger.log(
            username, "虚拟机", "删除快照", "成功", role,
            f"虚拟机: {vm_name} 删除快照: {snapshot_name} 成功"
        )
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "删除快照", "失败", role,
            f"虚拟机: {vm_name} 删除快照: {snapshot_name} 失败, 错误: {error}"
        )
    return form

@app.task(name="restore_vm_snapshot_callback")
def restore_vm_snapshot_callback(form):
    """
    还原虚拟机快照回调
    :param form:
    :return:
    """
    print("restore_vm_snapshot_callback 参数:", form)
    task_status = form.get("task_status", "failed")
    vm_name = form.get("vm_name", "")
    snapshot_name = form.get("snapshot_name", "")
    role = form.get("role", "")
    username = form.get("username", "")

    if task_status == "success":
        new_logger.log(
            username, "虚拟机", "还原快照", "成功", role,
            f"虚拟机: {vm_name} 还原快照: {snapshot_name} 成功"
        )
    else:
        error = form.get("error", "未知错误")
        new_logger.log(
            username, "虚拟机", "还原快照", "失败", role,
            f"虚拟机: {vm_name} 还原快照: {snapshot_name} 失败, 错误: {error}"
        )
    return form
